<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\ErrorResponseDto;
use App\Dto\Project\CreateProjectCategoryRequest;
use App\Dto\Project\CreateProjectCategoryResponse;
use App\Dto\Project\DeleteProjectCategoryResponse;
use App\Dto\Project\ProjectCategoryDto;
use App\Dto\Project\UpdateProjectCategoryRequest;
use App\Dto\Project\UpdateProjectCategoryResponse;
use App\Entity\ProjectCategory;
use App\Entity\User\User;
use App\Service\ProjectCategoryService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/categories')]
#[OA\Tag(name: 'Categories')]
#[Nelmio\Security(name: 'Bearer')]
class CategoryController extends AbstractController
{
    public function __construct(protected readonly ProjectCategoryService $projectCategoryService)
    {
    }

    #[Route(path: '', name: 'get_categories', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'List all categories',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(ref: new Nelmio\Model(type: ProjectCategoryDto::class))
        )
    )]
    public function list(): JsonResponse
    {
        return $this->json($this->projectCategoryService->getAll());
    }

    #[Route(path: '/user-categories/{email?}', name: 'get_user_categories', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'Returns User Categories',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(ref: new Nelmio\Model(type: ProjectCategoryDto::class))
        )
    )]
    public function user(#[CurrentUser] User $user, ?string $email = null): JsonResponse
    {
        $email = $email ?? $user->getEmail();
        return $this->json($this->projectCategoryService->getByUser($email), 200, [], ['groups' => ['id', 'user:read']]);
    }

    #[Route(path: '', name: 'create_category', methods: ['POST'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\RequestBody(
        description: 'Create Project Category',
        required: true,
        content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateProjectCategoryRequest::class))
    )]
    #[OA\Response(
        response: 201,
        description: 'Created successfully',
        content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateProjectCategoryResponse::class))
    )]
    #[OA\Response(
        response: 400,
        description: 'Error creating Project Category',
        content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
    )]
    public function create(
        #[MapRequestPayload(acceptFormat: 'json')]
        CreateProjectCategoryRequest $request,
    ): JsonResponse {
        $category = $this->projectCategoryService->create($request);
        return $this->json(new CreateProjectCategoryResponse($category), Response::HTTP_CREATED);
    }

    #[Route(path: '/{category}', name: 'get_category', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'Returns Project Category',
        content: new OA\JsonContent(ref: new Nelmio\Model(type: ProjectCategoryDto::class))
    )]
    public function read(ProjectCategory $category): JsonResponse
    {
        return $this->json(ProjectCategoryDto::create($category));
    }

    #[Route(path: '/{id:category}', name: 'update_category', methods: ['PUT'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\RequestBody(
        description: 'Update Project Category name',
        required: true,
        content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateProjectCategoryRequest::class))
    )]
    #[OA\Response(
        response: 200,
        description: 'Category updated successfully',
        content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateProjectCategoryResponse::class))
    )]
    #[OA\Response(
        response: 400,
        description: 'Category not found',
        content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
    )]
    public function update(
        ProjectCategory              $category,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdateProjectCategoryRequest $request,
    ): JsonResponse {
        $this->projectCategoryService->update($category, $request);
        return $this->json(new UpdateProjectCategoryResponse(), Response::HTTP_OK);
    }

    #[Route(path: '/{id:category}', name: 'delete_category', methods: ['DELETE'])]
    #[IsGranted('ROLE_SUPER_ADMIN')]
    #[OA\Response(
        response: 200,
        description: 'Category deleted successfully',
        content: new OA\JsonContent(ref: new Nelmio\Model(type: DeleteProjectCategoryResponse::class))
    )]
    #[OA\Response(
        response: 404,
        description: 'Category not found',
        content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
    )]
    public function delete(ProjectCategory $category): JsonResponse
    {
        $this->projectCategoryService->delete($category);
        return $this->json(new DeleteProjectCategoryResponse(), Response::HTTP_OK);
    }
}
