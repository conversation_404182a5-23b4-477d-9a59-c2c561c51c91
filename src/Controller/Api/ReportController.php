<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\Project;
use App\Service\Xml2\ReportService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route(path: '/api/v2/reports/{shortCode:project}')]
class ReportController extends AbstractController
{
    public function __construct(private readonly ReportService $reportService)
    {}


    #[Route(path: '/external-links-report', name: 'app_api_xml2_report_getExternalLinksReport', methods: ['GET'])]
    public function externalReferenceLinksReport(Project $project): Response
    {
        return $this->reportService->externalReferenceLinksReportCSV($project);
    }

    #[Route(path: '/chapters/{nodeId:node}/xml-validation.csv', methods: ['GET'])]
    public function chapterXmlValidationCsv(Project $project, AbstractCodeBookNode $node): Response
    {
        return $this->reportService->chapterXmlValidationCsv($project, $node);
    }

    #[Route(path: '/chapters/{nodeId:node}/is-was', name: 'app_api_xml2_report_getIsWasReport', methods: ['GET'])]
    public function getIsWasReport(Request $request, Project $project, AbstractCodeBookNode $node): Response
    {
        $trailing = json_decode($request->query->get('trailing'), true);
        $chapterType =  $request->query->get('chapterType');

        return $this->reportService->getIsWasReport($project, $node, $trailing);
    }
}
