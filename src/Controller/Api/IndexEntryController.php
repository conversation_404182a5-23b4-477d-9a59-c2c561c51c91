<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\Xml2\Index\CreateIndexEntryRequest;
use App\Dto\Xml2\Index\CreateIndexEntryResponse;
use App\Dto\Xml2\Index\DeleteIndexEntryResponse;
use App\Dto\Xml2\Index\UpdateIndexEntryRequest;
use App\Dto\Xml2\Index\UpdateIndexEntryResponse;
use App\Entity\CodeBook\IndexEntry;
use App\Entity\Project;
use App\Serializer\Dto\Book\IndexEntryDto;
use App\Service\Xml2\IndexService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}', format: 'json')]
#[OA\Tag(name: 'Index')]
#[Nelmio\Security(name: 'Bearer')]
class IndexEntryController extends AbstractController
{
    public function __construct(private readonly IndexService $indexService)
    {
    }

    #[Route(path: '/index-entries', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'List all Index Entry from Project',
        responses: [
            new OA\Response(
                response: 200,
                description: 'List of Index Entry',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: new Nelmio\Model(type: IndexEntryDto::class))
                )
            ),
        ]
    )]
    public function index(
        Project $project,
    ): JsonResponse {
        $list = $this->indexService->list($project);
        return $this->json($list, Response::HTTP_OK);
    }

    #[Route(path: '/index-entries', methods: ['POST'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Post(
        summary: 'Create new Index Entry',
        requestBody: new OA\RequestBody(
            request: 'Create new Index Entry',
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateIndexEntryRequest::class))
        ),
        responses: [
            new OA\Response(
                response: Response::HTTP_CREATED,
                description: 'CREATED',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateIndexEntryResponse::class))
            ),
            new OA\Response(response: Response::HTTP_BAD_REQUEST, description: 'Bad Request'),
        ]
    )]
    public function create(
        Project                 $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        CreateIndexEntryRequest $request,
    ): JsonResponse {
        $indexEntry = $this->indexService->create($project->getCodeBook(), $request);
        return $this->json($indexEntry, Response::HTTP_CREATED);
    }

    #[Route(path: '/index-entries/{nodeId:indexEntry}', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Get index entry',
        responses: [
            new OA\Response(
                response: 200,
                description: 'Returns Index Entry as JSON',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: IndexEntryDto::class))
            ),
            new OA\Response(response: 404, description: 'Index Entry not found'),
        ]
    )]
    public function read(
        Project    $project,
        IndexEntry $indexEntry,
    ): JsonResponse {
        return $this->json($indexEntry, Response::HTTP_OK);
    }

    #[Route(path: '/index-entries/{nodeId:indexEntry}', methods: ['PUT'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Put(
        summary: 'Update Index Entry',
        requestBody: new OA\RequestBody(
            request: 'Update Index Entry',
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateIndexEntryRequest::class))
        ),
        responses: [
            new OA\Response(
                response: Response::HTTP_OK,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateIndexEntryResponse::class))
            ),
            new OA\Response(response: Response::HTTP_NOT_FOUND, description: 'Not Found'),
        ]
    )]
    public function update(
        Project                 $project,
        IndexEntry              $indexEntry,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdateIndexEntryRequest $request,
    ): JsonResponse {
        $response = $this->indexService->update($indexEntry, $request);
        return $this->json($response, Response::HTTP_OK);
    }

    #[Route(path: '/index-entries/{nodeId:indexEntry}', methods: ['DELETE'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Delete(
        summary: 'Delete index entry',
        responses: [
            new OA\Response(
                response: 200,
                description: 'Index Entry deleted successfully',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: DeleteIndexEntryResponse::class))
            ),
            new OA\Response(response: 404, description: 'Index Entry not found'),
        ]
    )]
    public function delete(
        Project    $project,
        IndexEntry $indexEntry,
    ): JsonResponse {
        $response = $this->indexService->delete($indexEntry);
        return $this->json($response, Response::HTTP_OK);
    }
}
