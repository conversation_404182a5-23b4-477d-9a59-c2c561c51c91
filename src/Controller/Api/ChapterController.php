<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\ErrorResponseDto;
use App\Dto\Xml2\Chapter\CreateChapterRequest;
use App\Dto\Xml2\Chapter\GetChapterSectionsResponse;
use App\Dto\Xml2\Chapter\UpdateChapterRequest;
use App\Dto\Xml2\XRefLink\XRefLinkValidationResult;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Chapter;
use App\Entity\Project;
use App\Exception\ApiException;
use App\Serializer\Dto\Book\ChapterDto;
use App\Service\Xml2\ChapterService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}', format: 'json')]
#[OA\Tag(name: 'Chapter')]
#[Nelmio\Security(name: 'Bearer')]
#[OA\Parameter(
    name: 'shortCode',
    description: 'The ID of the project.',
    in: 'path',
    required: true,
    schema: new OA\Schema(type: 'string')
)]
#[OA\Response(response: 401, description: 'Unauthorized')]
class ChapterController extends AbstractController
{
    public function __construct(
        private readonly ChapterService $chapterService,
    ) {
    }

    #[Route(path: '/chapters', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Retrieves a list of all Chapters',
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: new Nelmio\Model(type: ChapterDto::class))
                )
            ),
        ]
    )]
    public function index(
        Project $project,
    ): JsonResponse {
        $list = $this->chapterService->list($project);
        return $this->json($list, Response::HTTP_OK);
    }

    #[Route(path: '/chapters', methods: ['POST'])]
    #[Route(path: '/chapter', methods: ['POST'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Post(
        summary: 'Create a new Chapter',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateChapterRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 201,
                description: 'Created',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ChapterDto::class))
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function create(
        Project              $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        CreateChapterRequest $request,
    ): JsonResponse {
        $chapter = $this->chapterService->create($project, $request);
        return $this->json($chapter, Response::HTTP_CREATED);
    }

    #[Route(path: '/chapters/{nodeId:node}', methods: ['GET'])]
    #[Route(path: '/chapter/{nodeId:node}', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Retrieves details of a specific Chapter',
        parameters: [
            new OA\Parameter(name: 'nodeId', description: 'The ID of the Chapter', in: 'path', required: true),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ChapterDto::class))
            ),
            new OA\Response(response: 404, description: 'Not Found'),
        ]
    )]
    public function read(
        Project              $project,
        AbstractCodeBookNode $node,
    ): JsonResponse {
        return $this->json($node, Response::HTTP_OK);
    }

    #[Route(path: '/chapters/{nodeId:chapter}', methods: ['PUT'])]
    #[Route(path: '/chapter/{nodeId:chapter}', methods: ['PUT'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Put(
        summary: 'Updates an existing Chapter',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateChapterRequest::class))
        ),
        parameters: [
            new OA\Parameter(name: 'nodeId', description: 'The ID of the Chapter', in: 'path', required: true),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ChapterDto::class))
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
            new OA\Response(response: 404, description: 'Not Found'),
        ]
    )]
    public function update(
        Project              $project,
        Chapter              $chapter,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdateChapterRequest $request,
    ): JsonResponse {
        $this->chapterService->update($project, $chapter, $request);
        return $this->json($chapter, Response::HTTP_OK);
    }

    #[Route(path: '/chapters/{nodeId:chapter}', methods: ['DELETE'])]
    #[Route(path: '/chapter/{nodeId:chapter}', methods: ['DELETE'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Delete(
        summary: 'Deletes a specific Chapter',
        parameters: [
            new OA\Parameter(name: 'nodeId', description: 'The ID of the Chapter', in: 'path', required: true),
        ],
        responses: [
            new OA\Response(response: 204, description: 'No Content'),
            new OA\Response(response: 404, description: 'Not Found'),
        ]
    )]
    public function delete(
        Project $project,
        Chapter $chapter,
    ): JsonResponse {
        $this->chapterService->delete($chapter);
        return $this->json($chapter, Response::HTTP_OK);
    }

    #[Route(path: '/chapters/{nodeId:node}/sections', methods: ['GET'])]
    #[Route(path: '/chapter/{nodeId:node}/sections', methods: ['GET'])]
    #[Route(path: '/chapter/{ulid:node}/sections', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Response(
        response: 200,
        description: 'Section Response',
        content: new OA\JsonContent(ref: new Nelmio\Model(type: GetChapterSectionsResponse::class))
    )]
    #[OA\Response(response: 404, description: 'Node not found')]
    public function sections(
        Project              $project,
        AbstractCodeBookNode $node,
        Request              $request,
    ): JsonResponse {
        $isReferenceLinks = filter_var($request->query->get('referencelinks', 'false'), FILTER_VALIDATE_BOOLEAN);
        return $this->json($this->chapterService->getSections($node, $isReferenceLinks));
    }

    #[Route(path: '/chapter/{ulid}/reference-sections', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Response(
        response: 200,
        description: 'Referece Section Response',
        content: new OA\JsonContent(ref: new Nelmio\Model(type: GetChapterSectionsResponse::class))
    )]
    #[OA\Response(response: 404, description: 'Node not found')]
    public function referenceSections(
        Project $project,
        string  $ulid,
    ): JsonResponse {
        return $this->json($this->chapterService->getReferenceSections($ulid));
    }

    #[Route(path: '/chapter/{nodeId:node}/links-report', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Response(
        response: 200,
        description: 'Get All Links Validation Response',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(ref: new Nelmio\Model(type: XRefLinkValidationResult::class))
        )
    )]
    #[OA\Response(response: 404, description: 'Get All Links not found')]
    public function getAllLinksInChapter(
        Project              $project,
        AbstractCodeBookNode $node,
    ): JsonResponse {
        try {
            $results = $this->chapterService->getAllLinksValidationResult($project, $node);
            return $this->json($results);
        } catch (ApiException $e) {
            return $this->json(new ErrorResponseDto($e->getMessage(), $e->getCode()), $e->getCode());
        }
    }
}
