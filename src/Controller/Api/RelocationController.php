<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\Xml2\Relocation\GetRelocationOptionsRequest;
use App\Entity\Project;
use App\Service\Xml2\RelocationService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}')]
#[IsGranted('EDIT_PROJECT', 'project')]
#[OA\Tag(name: 'Relocation')]
#[Nelmio\Security(name: 'Bearer')]
class RelocationController extends AbstractController
{
    public function __construct(private readonly RelocationService $relocationService)
    {
    }

    #[Route(path: '/relocation-options', methods: ['GET'])]
//    #[OA\Get(
//        summary: 'Get relocation options',
//        requestBody: new OA\RequestBody(
//            description: 'Get relocation Options Request',
//            required: true,
//            content: new OA\JsonContent(ref: new Nelmio\Model(type: GetRelocationOptionsRequest::class))
//        ),
//        responses: [
//            new OA\Response(
//                response: 200,
//                description: 'Get relocation Options Response',
//                content: new OA\JsonContent(ref: new Nelmio\Model(type: GetRelocationOptionsResponse::class))
//            ),
//            new OA\Response(
//                response: 404,
//                description: 'Error getting relocation options',
//                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
//            ),
//        ]
//    )]
    public function getRelocationOptions(
        Project                     $project,
        #[MapQueryString]
        GetRelocationOptionsRequest $request,
    ): JsonResponse {
        $response = $this->relocationService->getRelocationOptions($project, $request);
        return $this->json($response, Response::HTTP_OK);
    }

    #[Route(path: '/wrap-options', methods: ['GET'])]
//    #[OA\Get(
//        summary: 'Get wrap options',
//        requestBody: new OA\RequestBody(
//            description: 'Get wrap Options Request',
//            required: true,
//            content: new OA\JsonContent(ref: new Nelmio\Model(type: GetRelocationOptionsRequest::class))
//        ),
//        responses: [
//            new OA\Response(
//                response: 200,
//                description: 'Get wrap Options Response',
//                content: new OA\JsonContent(ref: new Nelmio\Model(type: GetRelocationOptionsResponse::class))
//            ),
//            new OA\Response(
//                response: 404,
//                description: 'Error getting wrap options',
//                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
//            ),
//        ]
//    )]
    public function getWrapOptions(
        Project                     $project,
        #[MapQueryString]
        GetRelocationOptionsRequest $request,
    ): JsonResponse {
        $response = $this->relocationService->getWrapOptions($project, $request);
        return $this->json($response, Response::HTTP_OK);
    }
}
