<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\ErrorResponseDto;
use App\Dto\Xml2\Book\ListBookResponse;
use App\Dto\Xml2\Book\ListContentsResponse;
use App\Dto\Xml2\Book\NodeParentsResponse;
use App\Dto\Xml2\Book\NodeReferencingResponse;
use App\Dto\Xml2\Project\ExportProjectRequest;
use App\Entity\Project;
use App\Message\Project\ExportProjectMessage;
use App\Repository\ProjectRepository;
use App\Service\Xml2\ContentsService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books', format: 'json')]
#[Nelmio\Security(name: 'Bearer')]
#[OA\Tag(name: 'Book')]
class BookController extends AbstractController
{
    public function __construct(
        private readonly ContentsService     $contentsService,
        private readonly MessageBusInterface $bus,
    ) {
    }

    #[Route(path: '', methods: ['GET'])]
    #[OA\Get(
        summary: 'List books available to user',
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ListBookResponse::class))
            ),
        ]
    )]
    public function list(ProjectRepository $repository): JsonResponse
    {
        $projects = $repository->findByUser($this->getUser());
        return $this->json(new ListBookResponse($projects));
    }

    #[Route(path: '/{shortCode:project}/contents', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'List contents of a book',
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ListContentsResponse::class))
            ),
            new OA\Response(
                response: 400,
                description: 'API Exception',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function contents(Project $project): JsonResponse
    {
        return $this->json($this->contentsService->getContents($project), Response::HTTP_OK);
    }

    #[Route(path: '/{ulid}/parents', methods: ['GET'])]
    #[OA\Get(
        summary: 'List node parents',
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: NodeParentsResponse::class))
            ),
            new OA\Response(
                response: 400,
                description: 'API Exception',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function getNodeParents(string $ulid): JsonResponse
    {
        return $this->json($this->contentsService->getNodeParents($ulid), Response::HTTP_OK);
    }

    #[Route(path: '/{shortCode:project}/section-xml', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Node Reference Response',
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: NodeReferencingResponse::class))
            ),
            new OA\Response(
                response: 400,
                description: 'API Exception',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function getNodeReferencing(Request $request, Project $project): JsonResponse
    {
        $nodeId = $request->query->get('rid', '');

        if (empty($nodeId)) {
            return $this->json([
                'message' => 'The "rid" query parameter is required and cannot be empty.',
            ], Response::HTTP_BAD_REQUEST);
        }

        return $this->json($this->contentsService->getNodeReferencing($project, $nodeId));
    }

    #[Route(path: '/{shortCode:project}/chub-export', methods: ['POST'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    public function cHubExport(
        Project              $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        ExportProjectRequest $request,
    ): JsonResponse {
        $request->shortCode = $project->getShortCode();
        $request->contentHub = true;

        $message = new ExportProjectMessage($request);
        $this->bus->dispatch($message);

        return $this->json(['message' => 'Uploaded successfully.', 'status' => 200]);
    }
}
