<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Entity\User\User;
use App\Security\CognitoUserProvider;
use App\Security\Model\UserInterface;
use App\Service\UserService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api')]
#[OA\Tag(name: 'Users')]
#[Nelmio\Security(name: 'Bearer')]
class UserController extends AbstractController
{
    public function __construct(private readonly UserService $userService)
    {
    }

    #[Route(path: '/user/import', methods: ['GET'])]
    #[IsGranted('ROLE_SUPER_ADMIN')]
    #[OA\Parameter(
        name: 'email',
        description: 'Email of the Cognito user to import',
        in: 'query',
        required: true,
        schema: new OA\Schema(type: 'string', format: 'email')
    )]
    #[OA\Response(
        response: 200,
        description: 'User imported successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                new OA\Property(property: 'lastName', type: 'string', example: 'Doe'),
                new OA\Property(property: 'roles', type: 'array', items: new OA\Items(type: 'string', example: 'ROLE_ADMIN')),
                new OA\Property(property: 'createdAt', type: 'string', format: 'date-time'),
                new OA\Property(property: 'updatedAt', type: 'string', format: 'date-time'),
            ],
            type: 'object'
        )
    )]
    #[OA\Response(response: 404, description: 'User not found')]
    public function importUser(Request $request, CognitoUserProvider $cognitoUserProvider): JsonResponse
    {
        $email = $request->query->get('email');
        $userFromCognito = $cognitoUserProvider->getCognitoUserDetails($email);

        if ($userFromCognito) {
            $user = $this->userService->createUser($userFromCognito);
            return $this->json($user, Response::HTTP_OK, [], ['groups' => ['getUser']]);
        }

        return $this->json(['message' => 'User not found.'], Response::HTTP_NOT_FOUND);
    }

    #[Route(path: '/users', name: 'user_list', methods: ['GET'])]
    #[IsGranted('ROLE_SUPER_ADMIN')]
    #[OA\Response(
        response: 200,
        description: 'List of all users',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                    new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                    new OA\Property(property: 'lastName', type: 'string', example: 'Doe'),
                    new OA\Property(property: 'roles', type: 'array', items: new OA\Items(type: 'string', example: 'ROLE_ADMIN')),
                    new OA\Property(property: 'createdAt', type: 'string', format: 'date-time'),
                    new OA\Property(property: 'updatedAt', type: 'string', format: 'date-time'),
                ],
                type: 'object'
            )
        )
    )]
    public function list(): JsonResponse
    {
        $users = $this->userService->getUsers();
        return $this->json($users, Response::HTTP_OK, [], ['groups' => ['getUser']]);
    }

    #[Route(path: '/users/{id:user}', name: 'user_show', methods: ['GET'])]
    #[IsGranted('ROLE_SUPER_ADMIN')]
    #[OA\Parameter(
        name: 'id',
        description: 'User ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'User details',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                new OA\Property(property: 'lastName', type: 'string', example: 'Doe'),
                new OA\Property(property: 'roles', type: 'array', items: new OA\Items(type: 'string', example: 'ROLE_ADMIN')),
                new OA\Property(property: 'createdAt', type: 'string', format: 'date-time'),
                new OA\Property(property: 'updatedAt', type: 'string', format: 'date-time'),
            ],
            type: 'object'
        )
    )]
    #[OA\Response(response: 404, description: 'User not found')]
    public function show(User $user): JsonResponse
    {
        return $this->json($user, Response::HTTP_OK, [], ['groups' => ['getUser']]);
    }

    #[Route(path: '/users', name: 'user_create', methods: ['POST'])]
    #[IsGranted('ROLE_SUPER_ADMIN')]
    #[OA\RequestBody(
        description: 'User creation data',
        required: true,
        content: new OA\JsonContent(
            required: ['email', 'firstName', 'lastName'],
            properties: [
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                new OA\Property(property: 'firstName', type: 'string', example: 'Alice'),
                new OA\Property(property: 'lastName', type: 'string', example: 'Smith'),
                new OA\Property(property: 'roles', type: 'array', items: new OA\Items(type: 'string', example: 'ROLE_VIEW')),
            ],
            type: 'object'
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'User created',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 42),
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                new OA\Property(property: 'firstName', type: 'string', example: 'Alice'),
                new OA\Property(property: 'lastName', type: 'string', example: 'Smith'),
                new OA\Property(property: 'roles', type: 'array', items: new OA\Items(type: 'string', example: 'ROLE_VIEW')),
                new OA\Property(property: 'createdAt', type: 'string', format: 'date-time'),
                new OA\Property(property: 'updatedAt', type: 'string', format: 'date-time'),
            ],
            type: 'object'
        )
    )]
    public function create(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $user = $this->userService->createUser($data);
        return $this->json($user, Response::HTTP_CREATED, [], ['groups' => ['getUser']]);
    }

    #[Route(path: '/users/{id:user}', name: 'user_update', methods: ['PUT'])]
    #[IsGranted('ROLE_SUPER_ADMIN')]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\RequestBody(
        description: 'User update data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'email', type: 'string', format: 'email', nullable: true),
                new OA\Property(property: 'firstName', type: 'string', nullable: true),
                new OA\Property(property: 'lastName', type: 'string', nullable: true),
                new OA\Property(property: 'roles', type: 'array', items: new OA\Items(type: 'string'), nullable: true),
            ],
            type: 'object'
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'User updated',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 42),
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                new OA\Property(property: 'firstName', type: 'string', example: 'Alice'),
                new OA\Property(property: 'lastName', type: 'string', example: 'Smith'),
                new OA\Property(property: 'roles', type: 'array', items: new OA\Items(type: 'string', example: 'ROLE_VIEW')),
                new OA\Property(property: 'createdAt', type: 'string', format: 'date-time'),
                new OA\Property(property: 'updatedAt', type: 'string', format: 'date-time'),
            ],
            type: 'object'
        )
    )]
    public function update(Request $request, User $user): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $updatedUser = $this->userService->updateUser($user, $data);
        return $this->json($updatedUser, Response::HTTP_OK, [], ['groups' => ['getUser']]);
    }

    #[Route(path: '/users/{id:user}', name: 'user_delete', methods: ['DELETE'])]
    #[IsGranted('ROLE_SUPER_ADMIN')]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'User deleted',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'status', type: 'boolean', example: true),
            ],
            type: 'object'
        )
    )]
    public function delete(User $user): JsonResponse
    {
        return $this->json([
            'status' => $this->userService->deleteUser($user),
        ]);
    }

    #[Route(path: '/session', methods: ['GET'])]
    #[OA\Get(
        summary: 'Get current user session information',
        description: 'Returns the current authenticated user information for session management',
        responses: [
            new OA\Response(
                response: 200,
                description: 'Current user session data',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'user', type: 'object', properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                            new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                            new OA\Property(property: 'lastName', type: 'string', example: 'Doe'),
                            new OA\Property(property: 'roles', type: 'array', items: new OA\Items(type: 'string', example: 'ROLE_ADMIN')),
                            new OA\Property(property: 'createdAt', type: 'string', format: 'date-time'),
                            new OA\Property(property: 'updatedAt', type: 'string', format: 'date-time'),
                        ]),
                        new OA\Property(property: 'authenticated', type: 'boolean', example: true),
                        new OA\Property(property: 'permissions', type: 'array', items: new OA\Items(type: 'string')),
                    ],
                    type: 'object'
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized - Invalid or missing token'),
        ]
    )]
    public function getSession(): JsonResponse
    {
        $user = $this->getUser();

        if (!$user) {
            return $this->json(['authenticated' => false], Response::HTTP_UNAUTHORIZED);
        }

        return $this->json([
            'authenticated' => true,
            'user'          => $this->userService->getByEmail($user->getUserIdentifier()),
            'permissions'   => $user->getRoles(),
        ], Response::HTTP_OK, [], ['groups' => ['getUser']]);
    }

    #[Route(path: '/user/roles', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'List of available roles',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'name', type: 'string', example: 'Admin User'),
                    new OA\Property(property: 'role', type: 'string', example: 'ROLE_ADMIN'),
                ],
                type: 'object'
            )
        )
    )]
    public function getRoles(): JsonResponse
    {
        return $this->json([
            ['name' => 'Codes User', 'role' => UserInterface::ROLE_CODES],
            ['name' => 'Pubs User', 'role' => UserInterface::ROLE_PUBS],
            ['name' => 'Admin User', 'role' => UserInterface::ROLE_ADMIN],
            ['name' => 'View User', 'role' => UserInterface::ROLE_VIEW],
        ]);
    }
}
