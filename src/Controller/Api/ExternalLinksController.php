<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\ErrorResponseDto;
use App\Dto\Xml2\Book\NodeReferencingResponse;
use App\Entity\Project;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}')]
#[OA\Tag(name: 'External Links')]
#[Nelmio\Security(name: 'Bearer')]
#[IsGranted('VIEW_PROJECT', 'project')]
class ExternalLinksController extends AbstractController
{
    #[Route(path: '/update-external-links', methods: ['GET'])]
    #[OA\Get(
        summary: 'Update external links',
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: NodeReferencingResponse::class))
            ),
            new OA\Response(
                response: 400,
                description: 'API Exception',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function updateLinks(
        Project $project,
    ): JsonResponse {
        return $this->json(['message' => 'External links updated'], Response::HTTP_OK);
    }

    #[Route(path: '/external-links', methods: ['GET'])]
    #[OA\Get(summary: 'List of External Links')]
    public function read(
        Project $project,
    ): JsonResponse {
        return $this->json([]);
    }

    #[Route(path: '/external-links', methods: ['PUT'])]
    public function putExternalLinks(
        Request $request,
        Project $project,
    ): JsonResponse {
        return $this->json([]);
    }
}
