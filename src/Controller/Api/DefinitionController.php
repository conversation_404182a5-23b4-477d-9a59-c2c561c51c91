<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\ErrorResponseDto;
use App\Dto\Xml2\Definition\CreateDefinitionRequest;
use App\Dto\Xml2\Definition\ListDefinitionsResponse;
use App\Dto\Xml2\Definition\MoveDefinitionRequest;
use App\Dto\Xml2\Definition\UpdateDefinitionRequest;
use App\Entity\CodeBook\Definition;
use App\Entity\Project;
use App\Exception\ApiException;
use App\Serializer\Dto\Book\DefinitionItemDto;
use App\Service\Xml2\DefinitionService;
use App\Service\Xml2\SearchService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}')]
#[OA\Tag(name: 'Definition')]
#[Nelmio\Security(name: 'Bearer')]
class DefinitionController extends AbstractController
{
    public function __construct(
        private readonly DefinitionService $definitionService,
        private readonly SearchService     $searchService
    ) {
    }

    #[Route(path: '/definitions', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'List all definitions',
        responses: [
            new OA\Response(
                response: Response::HTTP_OK,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ListDefinitionsResponse::class)),
            ),
        ]
    )]
    public function list(
        Project $project,
    ): JsonResponse {
        return $this->json($this->definitionService->list($project), Response::HTTP_OK);
    }

    #[Route(path: '/definitions', methods: ['POST'])]
    #[Route(path: '/chapters/{chapterId}/definition', methods: ['POST'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Post(
        summary: 'Create new Definition',
        requestBody: new OA\RequestBody(
            required: true,
            content: new Nelmio\Model(type: CreateDefinitionRequest::class),
        ),
        responses: [
            new OA\Response(
                response: Response::HTTP_CREATED,
                description: 'CREATED',
                content: new Nelmio\Model(type: DefinitionItemDto::class),
            ),
            new OA\Response(
                response: Response::HTTP_BAD_REQUEST,
                description: 'Error',
                content: new Nelmio\Model(type: ErrorResponseDto::class),
            ),
        ]
    )]
    public function create(
        Project                 $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        CreateDefinitionRequest $request,
        string                  $chapterId = '',
    ): JsonResponse {
        $definition = $this->definitionService->create($project, $request);
        return $this->json($definition, Response::HTTP_CREATED);
    }

    #[Route(path: '/definitions/{nodeId:definition}', methods: ['GET'])]
    #[Route(path: '/chapters/{chapterId}/definitions/{nodeId:definition}', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Get Definition',
        responses: [
            new OA\Response(
                response: Response::HTTP_OK,
                description: 'OK',
                content: new Nelmio\Model(type: DefinitionItemDto::class),
            ),
            new OA\Response(
                response: Response::HTTP_NOT_FOUND,
                description: 'Not Found',
                content: new Nelmio\Model(type: ErrorResponseDto::class),
            ),
        ]
    )]
    public function read(
        Project    $project,
        Definition $definition,
    ): JsonResponse {
        return $this->json($definition, Response::HTTP_OK);
    }

    #[Route(path: '/definitions/{nodeId:definition}', methods: ['PUT'])]
    #[Route(path: '/chapters/{chapterId}/definitions/{nodeId:definition}', methods: ['PUT'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Update Definition',
        requestBody: new OA\RequestBody(
            required: true,
            content: new Nelmio\Model(type: UpdateDefinitionRequest::class)
        ),
        responses: [
            new OA\Response(
                response: Response::HTTP_ACCEPTED,
                description: 'ACCEPTED',
                content: new Nelmio\Model(type: DefinitionItemDto::class),
            ),
            new OA\Response(
                response: Response::HTTP_BAD_REQUEST,
                description: 'Error',
                content: new Nelmio\Model(type: ErrorResponseDto::class),
            ),
        ]
    )]
    public function update(
        Project                 $project,
        Definition              $definition,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdateDefinitionRequest $request,
    ): JsonResponse {
        $this->definitionService->update($definition, $request);
        return $this->json($definition, Response::HTTP_ACCEPTED);
    }

    #[Route(path: '/definitions/{nodeId:definition}', methods: ['DELETE'])]
    #[Route(path: '/chapters/{chapterId}/definitions/{nodeId:definition}', methods: ['DELETE'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Delete(
        summary: 'Delete Definition',
        responses: [
            new OA\Response(
                response: Response::HTTP_ACCEPTED,
                description: 'ACCEPTED',
                content: new Nelmio\Model(type: DefinitionItemDto::class),
            ),
            new OA\Response(
                response: Response::HTTP_BAD_REQUEST,
                description: 'Error',
                content: new Nelmio\Model(type: ErrorResponseDto::class),
            ),
        ],
    )]
    public function delete(
        Project    $project,
        Definition $definition,
    ): JsonResponse {
        $this->definitionService->delete($definition);
        return $this->json($definition, Response::HTTP_ACCEPTED);
    }

    #[Route(path: '/definitions/{nodeId:definition}/move', methods: ['PUT'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Put(
        summary: 'Move Definition',
        requestBody: new OA\RequestBody(),
        responses: [
            new OA\Response(
                response: Response::HTTP_ACCEPTED,
                description: 'ACCEPTED',
                content: new Nelmio\Model(type: DefinitionItemDto::class),
            ),
            new OA\Response(
                response: Response::HTTP_BAD_REQUEST,
                description: 'Error',
                content: new Nelmio\Model(type: ErrorResponseDto::class),
            ),
        ],
    )]
    public function move(
        Project               $project,
        Definition            $definition,
        #[MapRequestPayload(acceptFormat: 'json')]
        MoveDefinitionRequest $request,
    ): JsonResponse {
        $this->definitionService->move($project, $definition, $request);
        return $this->json($definition, Response::HTTP_ACCEPTED);
    }

    #[Route(path: '/definitions-formal-usage', methods: ['PUT'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Put(
        summary: 'Set formal usage',
        responses: [
            new OA\Response(
                response: Response::HTTP_BAD_REQUEST,
                description: 'Formal Indicator Response',
            ),
        ],
    )]
    public function toggleFormalUsage(
        Project $project,
    ): Response {
        throw new ApiException('not implemented/used');
    }

    #[Route(path: '/definitions-usage', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'List formal usage cases',
        responses: [
            new OA\Response(
                response: Response::HTTP_OK,
                description: 'OK',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: '@id',
                            type: 'string',
                            example: 'http://127.0.0.1:8201/api/janus/books/SyncIPMC/definitions-usage?query=HOUSEKEEPING%20UNIT&page=1&limit=1'
                        ),
                        new OA\Property(
                            property: 'firstPage',
                            type: 'string',
                            example: 'http://127.0.0.1:8201/api/janus/books/SyncIPMC/definitions-usage?query=HOUSEKEEPING%20UNIT&page=1&limit=1'
                        ),
                        new OA\Property(property: 'previousPage', type: 'null'),
                        new OA\Property(
                            property: 'nextPage',
                            type: 'string',
                            example: 'http://127.0.0.1:8201/api/janus/books/SyncIPMC/definitions-usage?query=HOUSEKEEPING%20UNIT&page=2&limit=1'
                        ),
                        new OA\Property(
                            property: 'lastPage',
                            type: 'string',
                            example: 'http://127.0.0.1:8201/api/janus/books/SyncIPMC/definitions-usage?query=HOUSEKEEPING%20UNIT&page=5&limit=1'
                        ),
                        new OA\Property(property: 'pageCount', type: 'integer', example: 5),
                        new OA\Property(property: 'total', type: 'integer', example: 5),
                        new OA\Property(
                            property: 'member',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(
                                        property: 'section',
                                        properties: [
                                            new OA\Property(
                                                property: 'ordinal',
                                                type: 'string',
                                                example: "<span xmlns='http://www.w3.org/1999/xhtml'>201.5</span>"
                                            ),
                                            new OA\Property(property: 'label', type: 'null'),
                                            new OA\Property(
                                                property: 'title',
                                                type: 'string',
                                                example: "<span xmlns='http://www.w3.org/1999/xhtml'>Parts.</span>"
                                            ),
                                            new OA\Property(property: 'type', type: 'string', example: 'section'),
                                            new OA\Property(property: 'id', type: 'string', example: 'SyncIPMC_Ch02_Sec201.5'),
                                        ],
                                        type: 'object'
                                    ),
                                    new OA\Property(
                                        property: 'book',
                                        properties: [
                                            new OA\Property(property: 'title', type: 'string', example: 'SYNC IPMC TEST'),
                                            new OA\Property(property: 'id', type: 'string', example: 'SyncIPMC'),
                                        ],
                                        type: 'object'
                                    ),
                                    new OA\Property(
                                        property: 'chapter',
                                        properties: [
                                            new OA\Property(
                                                property: 'ordinal',
                                                type: 'string',
                                                example: "<span xmlns='http://www.w3.org/1999/xhtml'>2</span>"
                                            ),
                                            new OA\Property(
                                                property: 'label',
                                                type: 'string',
                                                example: "<span xmlns='http://www.w3.org/1999/xhtml'>CHAPTER</span>"
                                            ),
                                            new OA\Property(
                                                property: 'title',
                                                type: 'string',
                                                example: "<span xmlns='http://www.w3.org/1999/xhtml'>DEFINITIONS</span>"
                                            ),
                                            new OA\Property(property: 'id', type: 'string', example: 'SyncIPMC_Ch02'),
                                        ],
                                        type: 'object'
                                    ),
                                    new OA\Property(property: 'additionalContent', type: 'null'),
                                    new OA\Property(
                                        property: 'content',
                                        type: 'string',
                                        example: "<h1 class='level2'><span class='section_number'>201.5</span><span class='level2_title'>Parts.</span></h1><div>Whenever the words “<span class='formal_usage'>dwelling unit</span>,” “dwelling,” “<span class='formal_usage'>premises</span>,” “building,” “<span class='formal_usage'>rooming house</span>,” “<span class='formal_usage'>rooming unit</span>,” “<mark data-formal-usage='true' data-instance=1><span class='formal_usage'>housekeeping unit</span></mark>” or “story” are stated in this code, they shall be construed as though they were followed by the words “or any part thereof.” </div>"
                                    ),
                                    new OA\Property(property: 'id', type: 'string', example: 'SyncIPMC_Ch02_Sec201.5'),
                                ],
                                type: 'object'
                            )
                        ),
                        new OA\Property(property: 'limit', type: 'integer', example: 1),
                        new OA\Property(property: 'responseId', type: 'string', example: '5eb64461-fabe-4340-9adc-885ca7299ba7'),
                    ],
                    type: 'object'
                )
            ),
        ],
    )]
    public function getDefinitionUsage(
        Request $request,
        Project $project,
    ): JsonResponse {
        $response = $this->searchService->getDefinitionUsages(
            $project,
            $request->get('query'),
            $request->get('similarMatches', []),
            $request->get('page', 1),
            $request->get('limit', 10)
        );

        return $this->json($response, Response::HTTP_OK);
    }
}
