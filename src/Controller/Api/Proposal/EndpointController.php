<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api\Proposal;

use App\Dto\Xml2\Cdp\CycleSyncResponse;
use App\Dto\Xml2\Cdp\CycleSyncStatusResponse;
use App\Entity\Cdp\CdpEndpoint;
use App\Entity\User\User;
use App\Enum\CdpEndpointStatus;
use App\Message\Cdp\SyncProposalsFromEndpointMessage;
use Doctrine\ORM\EntityManagerInterface;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;

#[Route(path: '/api/v2/proposals')]
#[OA\Tag(name: 'Proposals')]
#[Nelmio\Security(name: 'Bearer')]
class EndpointController extends AbstractController
{
    public function __construct(
        private readonly MessageBusInterface    $messageBus,
        private readonly EntityManagerInterface $em
    ) {
    }

    #[Route(path: '/endpoint', methods: ['GET'])]
    #[OA\Get(
        summary: 'List cdpACCESS Endpoints',
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(
                    type: CdpEndpoint::class,
                    groups: ['id', 'endpoint:read', 'cycle:read', 'book:read']
                ))
            ),
        ]
    )]
    public function list(): JsonResponse
    {
        $endpoints = $this->em->getRepository(CdpEndpoint::class)->findAll();
        return $this->json($endpoints, 200, [], ['groups' => ['id', 'endpoint:read', 'cycle:read', 'book:read']]);
    }

    #[Route(path: '/endpoint/{id:endpoint}/sync', methods: ['GET'])]
    #[Route(path: '/{id:endpoint}/sync', methods: ['GET'])]
    #[OA\Get(
        summary: 'Request Cycle Sync',
        responses: [
            new OA\Response(
                response: 202,
                description: 'Accepted',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: CycleSyncResponse::class))
            ),
        ]
    )]
    public function sync(CdpEndpoint $endpoint, #[CurrentUser] User $user): Response
    {
        $endpoint->setStatus(CdpEndpointStatus::QUEUED);
        $this->em->flush();

        /** @var User $user */
        $user = $this->em->getRepository(User::class)->findOneBy([
            'email' => $user->getEmail(),
        ]);

        $message = new SyncProposalsFromEndpointMessage($endpoint, (string) $user);
        $this->messageBus->dispatch($message);

        return $this->json(new CycleSyncResponse(), Response::HTTP_ACCEPTED);
    }

    #[Route(path: '/endpoint/{id:endpoint}/status', methods: ['GET'])]
    #[Route(path: '/{id:endpoint}/status', methods: ['GET'])]
    #[OA\Get(
        summary: 'Sync Status',
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: CycleSyncStatusResponse::class))
            ),
        ]
    )]
    public function status(CdpEndpoint $endpoint): Response
    {
        return $this->json(CycleSyncStatusResponse::createFromResponse($endpoint), Response::HTTP_OK);
    }
}
