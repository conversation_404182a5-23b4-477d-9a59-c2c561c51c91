<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api\Cdp;

use App\Dto\Xml2\Cdp\CdpGetApprovedBySection;
use App\Dto\Xml2\Cdp\CdpGetProposalsList;
use App\Dto\Xml2\Cdp\CdpPostProposalContentResponse;
use App\Dto\Xml2\Cdp\CdpUpdateProposalRequest;
use App\Dto\Xml2\Cdp\CreateFromProposalActionRequest;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\Project;
use App\Enum\ProposalEvaluationStatus;
use App\Message\Project\Action\ReEvaluateWorkflowStatusMessage;
use App\Service\Xml2\CdpService;
use Doctrine\ORM\EntityManagerInterface;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

use function set_time_limit;

#[Route(path: '/api/v2/proposals')]
#[OA\Tag(name: 'Proposals')]
#[Nelmio\Security(name: 'Bearer')]
class CdpController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly CdpService             $cdpService,
        private readonly MessageBusInterface    $messageBus
    ) {
    }

    #[Route(path: '/{shortCode:project}/new-proposals', methods: ['GET'])]
    #[OA\Get(
        summary: 'Get proposals by book.',
        responses: [
            new OA\Response(
                response: Response::HTTP_OK,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: CdpGetProposalsList::class))
            ),
        ]
    )]
    public function list(
        Project $project,
    ): JsonResponse {
        // CT-4046 - temporary fix to help timeout issues for large amounts of changes (IFC)
        set_time_limit(300);
        $actions = $this->cdpService->getProposalsByBookId($project);
        return $this->json($actions, Response::HTTP_OK);
    }

    #[OA\Get(
        summary: 'Get approved changes by node.',
        responses: [
            new OA\Response(
                response: Response::HTTP_OK,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: CdpGetApprovedBySection::class))
            ),
        ]
    )]
    #[Route(path: '/{shortCode:project}/approved-changes/{nodeId:section}', methods: ['GET'])]
    public function listBySection(
        Project              $project,
        AbstractCodeBookNode $section,
    ): JsonResponse {
        $response = new CdpGetApprovedBySection($section->getCodeChanges()->toArray());
        return $this->json($response, Response::HTTP_OK);
    }

    #[OA\Post(
        summary: 'Post proposals content.',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateFromProposalActionRequest::class))
        ),
        responses: [
            new OA\Response(
                response: Response::HTTP_OK,
                description: 'Created',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: CdpPostProposalContentResponse::class))
            ),
        ]
    )]
    #[Route(path: '/{shortCode:project}/proposal-contents', methods: ['POST'])]
    public function create(
        Project                         $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        CreateFromProposalActionRequest $request,
    ): JsonResponse {
        $node = $this->cdpService->create($project, $request);
        return $this->json($node, Response::HTTP_CREATED);
    }

    #[OA\Put(
        summary: 'Put proposals content.',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CdpUpdateProposalRequest::class))
        )
    )]
    #[Route(path: '/{shortCode:project}/override-proposal-number', methods: ['PUT'])]
    public function update(
        Project                  $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        CdpUpdateProposalRequest $request,
    ): JsonResponse {
        $response = $this->cdpService->update($project, $request);
        return $this->json($response, Response::HTTP_OK);
    }

    #[IsGranted('ROLE_SUPER_ADMIN')]
    #[Route(path: '/{shortCode:project}/evaluate-proposals', methods: ['GET'])]
    public function evaluateByProject(
        Project $project,
    ): JsonResponse {
        $current = $project->getProposalsEvaluationStatus();
        if (\in_array($current, [ProposalEvaluationStatus::RUNNING, ProposalEvaluationStatus::QUEUED], true)) {
            return $this->json([
                'projectId' => $project->getId(),
                'status'    => $current,
                'reset'     => true,
                'mode'      => 'async',
                'message'   => 'Evaluation already in progress.',
            ], Response::HTTP_ACCEPTED);
        }

        $project->setProposalsEvaluationStatus(ProposalEvaluationStatus::QUEUED);
        $this->em->flush();

        $this->messageBus->dispatch(new ReEvaluateWorkflowStatusMessage($project->getId()));

        return $this->json([
            'projectId' => $project->getId(),
            'status'    => ProposalEvaluationStatus::QUEUED,
            'reset'     => true,
            'mode'      => 'async',
            'message'   => 'Workflow status re-evaluation queued for background processing.',
        ], Response::HTTP_OK);
    }

    #[IsGranted('ROLE_SUPER_ADMIN')]
    #[Route(path: '/{shortCode:project}/evaluation-status', methods: ['GET'])]
    public function status(
        Project $project,
    ): JsonResponse {
        $totalCodeChanges = count($project->getCodeChanges());

        return $this->json([
            'projectId'                 => $project->getId(),
            'status'                    => $project->getProposalsEvaluationStatus(),
            'lastProposalsEvaluationAt' => $project->getLastProposalsEvaluationAt()?->format(DATE_ATOM),
            'totalCodeChanges'          => $totalCodeChanges,
            'canTriggerEvaluation'      => !in_array($project->getProposalsEvaluationStatus(), [
                ProposalEvaluationStatus::RUNNING,
                ProposalEvaluationStatus::QUEUED,
            ]),
        ], Response::HTTP_OK);
    }
}
