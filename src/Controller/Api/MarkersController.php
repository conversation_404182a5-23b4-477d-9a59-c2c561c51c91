<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\Xml2\Markers\CreateMarkerResponse;
use App\Dto\Xml2\Markers\CreateMarkersRequest;
use App\Dto\Xml2\Markers\DeleteMarkerResponse;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\Project;
use App\Service\Xml2\MarkersService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}/marker', format: 'json')]
#[OA\Tag(name: 'Markers')]
#[Nelmio\Security(name: 'Bearer')]
#[OA\Parameter(
    name: 'shortCode',
    description: 'The ID of the project.',
    in: 'path',
    required: true,
    schema: new OA\Schema(type: 'string')
)]
#[OA\Response(response: 401, description: 'Unauthorized')]
class MarkersController extends AbstractController
{
    public function __construct(private readonly MarkersService $service)
    {
    }

    #[Route(path: '', methods: ['POST'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Post(
        summary: 'Create Marker',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateMarkersRequest::class))
        ),
        responses: [
            new OA\Response(
                response: Response::HTTP_CREATED,
                description: 'CREATED',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateMarkerResponse::class))
            ),
            new OA\Response(response: Response::HTTP_BAD_REQUEST, description: 'Create error'),
        ]
    )]
    public function create(
        Project              $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        CreateMarkersRequest $request,
    ): JsonResponse {
        $response = $this->service->create($project, $request);
        return $this->json($response, Response::HTTP_CREATED);
    }

    #[Route(path: '/{nodeId:bookNode}', methods: ['DELETE'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Delete(
        summary: 'Delete Marker',
        responses: [
            new OA\Response(
                response: Response::HTTP_OK,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: DeleteMarkerResponse::class))
            ),
            new OA\Response(response: Response::HTTP_BAD_REQUEST, description: 'Delete error'),
        ]
    )]
    public function delete(
        Project              $project,
        AbstractCodeBookNode $bookNode,
    ): JsonResponse {
        $response = $this->service->delete($bookNode);
        return $this->json($response, Response::HTTP_OK);
    }

    #[Route(path: '/deletion-marker/{nodeId:bookNode}', methods: ['DELETE'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Delete(
        summary: 'Delete Deletion Marker',
        responses: [
            new OA\Response(
                response: Response::HTTP_OK,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: DeleteMarkerResponse::class))
            ),
            new OA\Response(response: Response::HTTP_BAD_REQUEST, description: 'Delete error'),
        ]
    )]
    public function removeDeletionMarker(
        Project              $project,
        AbstractCodeBookNode $bookNode,
    ): JsonResponse {
        $response = $this->service->removeDeletionMarker($bookNode);
        return $this->json($response, Response::HTTP_OK);
    }
}
