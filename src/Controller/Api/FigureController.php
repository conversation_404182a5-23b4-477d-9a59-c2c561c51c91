<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\ErrorResponseDto;
use App\Dto\Xml2\DeleteNodeRequest;
use App\Dto\Xml2\Figure\CreateFigureRequest;
use App\Dto\Xml2\Figure\UpdateFigureRequest;
use App\Entity\CodeBook\Figure;
use App\Entity\Project;
use App\Serializer\Dto\Book\FigureDto;
use App\Service\Xml2\FigureService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}', format: 'json')]
#[OA\Tag(name: 'Figure')]
#[Nelmio\Security(name: 'Bearer')]
#[OA\Parameter(
    name: 'shortCode',
    description: 'The ID of the project.',
    in: 'path',
    required: true,
    schema: new OA\Schema(type: 'string')
)]
#[OA\Response(response: 401, description: 'Unauthorized')]
class FigureController extends AbstractController
{
    public function __construct(private readonly FigureService $figureService)
    {
    }

    #[Route(path: '/figures', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Retrieves a list of all Figures',
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: new Nelmio\Model(type: FigureDto::class))
                )
            ),
        ]
    )]
    public function list(
        Project $project,
    ): JsonResponse {
        $list = $this->figureService->list($project);
        return $this->json($list, Response::HTTP_OK);
    }

    #[Route(path: '/figures', methods: ['POST'])]
    #[Route(path: '/chapters/{chapterId}/figures', methods: ['POST'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Post(
        summary: 'Create Figure',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateFigureRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 201,
                description: 'Figure was created',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: FigureDto::class))
            ),
            new OA\Response(response: 400, description: 'Create error'),
        ]
    )]
    public function create(
        Project             $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        CreateFigureRequest $request,
    ): JsonResponse {
        $figure = $this->figureService->create($project, $request);
        return $this->json($figure, Response::HTTP_CREATED);
    }

    #[Route(path: '/figures/{nodeId:figure}', methods: ['GET'])]
    #[Route(path: '/chapters/{chapterId}/figures/{nodeId:figure}', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Retrieves details of a specific Figure',
        parameters: [
            new OA\Parameter(name: 'nodeId', description: 'The ID of the Figure', in: 'path', required: true),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: FigureDto::class))
            ),
            new OA\Response(
                response: 404,
                description: 'Not Found',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function read(
        Project $project,
        Figure  $figure,
    ): JsonResponse {
        return $this->json($figure, Response::HTTP_OK);
    }

    #[Route(path: '/figures/{nodeId:figure}', methods: ['PUT'])]
    #[Route(path: '/chapters/{chapterId}/figures/{nodeId:figure}', methods: ['PUT'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Put(
        summary: 'Updates an existing Figure',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateFigureRequest::class))
        ),
        parameters: [
            new OA\Parameter(name: 'nodeId', description: 'The ID of the Figure', in: 'path', required: true),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: FigureDto::class))
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
            new OA\Response(
                response: 404,
                description: 'Not Found',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function update(
        Project             $project,
        Figure              $figure,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdateFigureRequest $request,
    ): JsonResponse {
        $this->figureService->update($project, $figure, $request);
        return $this->json($figure, Response::HTTP_OK);
    }

    #[Route(path: '/figures/{nodeId:figure}', methods: ['DELETE'])]
    #[Route(path: '/chapters/{chapterId}/figures/{nodeId:figure}', methods: ['DELETE'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Delete(
        summary: 'Deletes a specific Figure',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: DeleteNodeRequest::class))
        ),
        parameters: [
            new OA\Parameter(name: 'nodeId', description: 'The ID of the Figure', in: 'path', required: true),
        ],
        responses: [
            new OA\Response(response: 204, description: 'No Content'),
            new OA\Response(
                response: 400,
                description: 'Bad Request',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
            new OA\Response(
                response: 404,
                description: 'Not Found',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function delete(
        Project           $project,
        Figure            $figure,
        #[MapRequestPayload(acceptFormat: 'json')]
        DeleteNodeRequest $request,
    ): JsonResponse {
        $this->figureService->delete($figure, $request);
        return $this->json(null, Response::HTTP_OK);
    }
}
