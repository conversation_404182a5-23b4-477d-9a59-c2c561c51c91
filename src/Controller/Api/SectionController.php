<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\Xml2\DeleteNodeRequest;
use App\Dto\Xml2\Section\CreateSectionRequest;
use App\Dto\Xml2\Section\CreateSectionResponse;
use App\Dto\Xml2\Section\DeleteSectionResponse;
use App\Dto\Xml2\Section\UpdateSectionRequest;
use App\Dto\Xml2\Section\UpdateSectionResponse;
use App\Dto\Xml2\Volume\VolumeResponse;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Volume;
use App\Entity\Project;
use App\Serializer\Dto\Book\SectionDto;
use App\Service\Xml2\SectionService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}', format: 'json')]
#[OA\Tag(name: 'Sections')]
#[Nelmio\Security(name: 'Bearer')]
class SectionController extends AbstractController
{
    public function __construct(private readonly SectionService $sectionService)
    {
    }

    #[IsGranted('VIEW_PROJECT', 'project')]
    #[Route(path: '/sections', methods: ['GET'])]
    #[OA\Get(
        summary: 'List all Sections from Project',
        responses: [
            new OA\Response(
                response: 200,
                description: 'List of Sections',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: new Nelmio\Model(type: SectionDto::class))
                )
            ),
        ]
    )]
    public function index(
        Project $project,
    ): JsonResponse {
        $list = $this->sectionService->list($project);
        return $this->json($list, Response::HTTP_OK);
    }

    #[IsGranted('EDIT_PROJECT', 'project')]
    #[Route(path: '/sections', methods: ['POST'])]
    #[Route(path: '/chapters/{chapterId}/sections', methods: ['POST'])]
    #[OA\Post(
        summary: 'Create Section',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateSectionRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 201,
                description: 'Section was created',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateSectionResponse::class))
            ),
            new OA\Response(response: 400, description: 'Error creating Section'),
        ]
    )]
    public function create(
        Project              $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        CreateSectionRequest $request,
        string               $chapterId = '',
    ): JsonResponse {
        $section = $this->sectionService->create($project, $request);
        return $this->json($section, Response::HTTP_CREATED);
    }

    #[Route(path: '/sections/{nodeId:node}', methods: ['GET'])]
    #[Route(path: '/chapters/{chapterId}/sections/{nodeId:node}', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Get Section',
        responses: [
            new OA\Response(
                response: 200,
                description: 'Get Section',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: SectionDto::class))
            ),
            new OA\Response(response: 404, description: 'Section not found'),
        ]
    )]
    public function read(
        Project              $project,
        AbstractCodeBookNode $node,
    ): JsonResponse {
        if ($node instanceof Volume) {
            return $this->json(new VolumeResponse($node), Response::HTTP_OK);
        }
        return $this->json($node);
    }

    #[Route(path: '/sections/{nodeId:section}', methods: ['PUT'])]
    #[Route(path: '/chapters/{chapterId}/sections/{nodeId:section}', methods: ['PUT'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Put(
        summary: 'Update Section',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateSectionRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 202,
                description: 'Section was updated',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateSectionResponse::class))
            ),
            new OA\Response(response: 400, description: 'Update error'),
            new OA\Response(response: 404, description: 'Section not found'),
        ]
    )]
    public function update(
        Project              $project,
        Section              $section,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdateSectionRequest $request,
    ): JsonResponse {
        $this->sectionService->update($project, $section, $request);
        return $this->json($section, Response::HTTP_ACCEPTED);
    }

    #[Route(path: '/chapters/{chapterId}/sections/{nodeId:section}/wrap/{wrapId}', methods: ['PUT'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Put(
        summary: 'Wrap Section',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateSectionRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 202,
                description: 'Section was updated',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateSectionResponse::class))
            ),
            new OA\Response(response: 400, description: 'Update error'),
            new OA\Response(response: 404, description: 'Section not found'),
        ]
    )]
    public function wrap(
        Project              $project,
        Section              $section,
        string               $wrapId,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdateSectionRequest $request,
    ): JsonResponse {
        $this->sectionService->wrap($project, $section, $wrapId, $request);
        return $this->json($section, Response::HTTP_ACCEPTED);
    }

    #[Route(path: '/sections/{nodeId:section}', methods: ['DELETE'])]
    #[Route(path: '/chapters/{chapterId}/sections/{nodeId:section}', methods: ['DELETE'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Delete(
        summary: 'Delete Section',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: DeleteNodeRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 202,
                description: 'Section deleted',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: DeleteSectionResponse::class))
            ),
            new OA\Response(response: 404, description: 'Section not found'),
        ]
    )]
    public function delete(
        Project           $project,
        Section           $section,
        #[MapRequestPayload(acceptFormat: 'json')]
        DeleteNodeRequest $request,
    ): JsonResponse {
        $response = $this->sectionService->delete($section, $request);
        return $this->json($response, Response::HTTP_OK);
    }
}
