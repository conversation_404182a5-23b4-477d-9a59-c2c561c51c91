<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\Xml2\Node\UpdateNodeNotesRequest;
use App\Dto\Xml2\Node\UpdateNodeNotesResponse;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\Project;
use App\Serializer\Dto\Book\NotesDto;
use App\Service\Xml2\NoteService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}/nodes')]
#[Route(path: '/api/v2/books/{shortCode:project}/notes')]
#[IsGranted('EDIT_PROJECT', 'project')]
#[OA\Tag(name: 'Notes')]
#[Nelmio\Security(name: 'Bearer')]
class NotesController extends AbstractController
{
    public function __construct(private readonly NoteService $noteService)
    {
    }

    #[Route(path: '/{ulid:node}/notes', methods: ['GET'])]
    #[OA\Get(
        summary: 'Get Notes',
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: NotesDto::class))
            ),
        ],
    )]
    public function readNotes(
        Project              $project,
        AbstractCodeBookNode $node,
    ): JsonResponse {
        return $this->json($this->noteService->readNotes($node));
    }

    #[Route(path: '/{ulid:node}/notes', methods: ['PUT'])]
    #[OA\Put(
        summary: 'Update notes',
        requestBody: new OA\RequestBody(
            required: true,
            content: new Nelmio\Model(type: UpdateNodeNotesRequest::class),
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateNodeNotesResponse::class))
            ),
        ],
    )]
    public function update(
        Project                $project,
        AbstractCodeBookNode   $node,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdateNodeNotesRequest $request,
    ): JsonResponse {
        return $this->json($this->noteService->updateNotes($node, $request), Response::HTTP_ACCEPTED);
    }
}
