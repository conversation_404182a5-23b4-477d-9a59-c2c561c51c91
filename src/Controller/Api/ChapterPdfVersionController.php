<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\ErrorResponseDto;
use App\Dto\Xml2\Chapter\GetChapterVersionsListResponse;
use App\Entity\ChapterPdfVersion;
use App\Entity\Project;
use App\Service\ChapterPdfVersionService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

use function mb_strlen;
use function rawurlencode;
use function sprintf;

#[Route(path: '/api/v2/books/{shortCode:project}')]
#[OA\Tag(name: 'Chapter PDF Versions')]
#[Nelmio\Security(name: 'Bearer')]
#[OA\Parameter(
    name: 'shortCode',
    description: 'The ID of the project.',
    in: 'path',
    required: true,
    schema: new OA\Schema(type: 'string')
)]
#[OA\Response(response: 401, description: 'Unauthorized')]
class ChapterPdfVersionController extends AbstractController
{
    public function __construct(
        private readonly ChapterPdfVersionService $pdfVersionService,
        private readonly string                   $apiUrl
    ) {
    }

    #[Route(path: '/chapter-versions', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Get Chapter PDF Versions List',
        responses: [
            new OA\Response(
                response: 200,
                description: 'Chapter Versions Response',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: GetChapterVersionsListResponse::class))
            ),
            new OA\Response(response: 404, description: 'No versions found'),
        ]
    )]
    public function getChapterVersions(Project $project): JsonResponse
    {
        $baseUrl = sprintf('%s/api/v2/books/%s', $this->apiUrl, $project->getShortCode());
        $result = $this->pdfVersionService->getChapterVersions($project, $baseUrl);

        return $this->json($result);
    }

    #[Route(path: '/pdf-version/{id:version}/{name}', name: 'api_chapter_pdf_version_download', requirements: ['filename' => '.+'], methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Download Chapter PDF Version',
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'The ID of the PDF version',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'integer')
            ),
            new OA\Parameter(
                name: 'filename',
                description: 'The filename of the PDF (must match the version filename)',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
        ],
        responses: [
            new OA\Response(response: 200, description: 'PDF Download'),
            new OA\Response(response: 404, description: 'PDF Version not found'),
            new OA\Response(response: 403, description: 'Access denied'),
        ]
    )]
    public function downloadPdf(Project $project, ChapterPdfVersion $version): Response
    {
        if ($version->getProject()->getId() !== $project->getId()) {
            return $this->json(
                new ErrorResponseDto('PDF version not found for this project', Response::HTTP_NOT_FOUND),
                Response::HTTP_NOT_FOUND
            );
        }

        try {
            $pdfContent = $this->pdfVersionService->downloadPdf($version);
            $filename = $version->getFilename();

            $response = new Response($pdfContent);
            $response->headers->set('Content-Type', 'application/pdf');
            $response->headers->set('Content-Disposition', sprintf('inline; filename="%s"; filename*=UTF-8\'\'%s', $filename, rawurlencode($filename)));
            $response->headers->set('Content-Length', (string) mb_strlen($pdfContent));

            return $response;
        } catch (\RuntimeException $e) {
            return $this->json(
                new ErrorResponseDto($e->getMessage(), Response::HTTP_NOT_FOUND),
                Response::HTTP_NOT_FOUND
            );
        }
    }
}
