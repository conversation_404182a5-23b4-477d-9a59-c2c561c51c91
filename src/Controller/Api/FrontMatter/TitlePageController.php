<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api\FrontMatter;

use App\Dto\ErrorResponseDto;
use App\Dto\Xml2\FrontMatter\CreateTitlePageRequest;
use App\Dto\Xml2\FrontMatter\DeleteTitlePageResponse;
use App\Dto\Xml2\FrontMatter\UpdateTitlePageRequest;
use App\Entity\CodeBook\TitlePage;
use App\Entity\Project;
use App\Serializer\Dto\Book\TitlePageDto;
use App\Service\Xml2\FrontMatter\TitlePageService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}/front-matter/title-page', format: 'json')]
#[OA\Tag(name: 'Front Matter')]
#[Nelmio\Security(name: 'Bearer')]
class TitlePageController extends AbstractController
{
    public function __construct(private readonly TitlePageService $titlePageService)
    {
    }

    #[Route(path: '', methods: ['POST'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Post(
        summary: 'Create Title Page',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateTitlePageRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 201,
                description: 'Created',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: TitlePageDto::class))
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function create(
        Project                $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        CreateTitlePageRequest $request,
    ): JsonResponse {
        $titlePage = $this->titlePageService->create($project, $request);
        return $this->json($titlePage, Response::HTTP_CREATED);
    }

    #[Route(path: '/{nodeId:titlePage}', methods: ['GET'])]
    #[Route(path: '/{ulid:titlePage}', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Get Title Page',
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: TitlePageDto::class))
            ),
            new OA\Response(response: 404, description: 'Not Found'),
        ]
    )]
    public function read(
        Project   $project,
        TitlePage $titlePage,
    ): JsonResponse {
        return $this->json($titlePage, Response::HTTP_OK);
    }

    #[Route(path: '/{nodeId:titlePage}', methods: ['PUT'])]
    #[Route(path: '/{ulid:titlePage}', methods: ['PUT'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Put(
        summary: 'Update Title Page',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateTitlePageRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 202,
                description: 'Updated',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: TitlePageDto::class))
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
            new OA\Response(response: 404, description: 'Not Found'),
        ]
    )]
    public function update(
        Project                $project,
        TitlePage              $titlePage,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdateTitlePageRequest $request,
    ): JsonResponse {
        $this->titlePageService->update($titlePage, $request);
        return $this->json($titlePage, Response::HTTP_OK);
    }

    #[Route(path: '/{nodeId:titlePage}', methods: ['DELETE'])]
    #[Route(path: '/{ulid:titlePage}', methods: ['DELETE'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Delete(
        summary: 'Delete Title Page',
        responses: [
            new OA\Response(
                response: 204,
                description: 'No Content',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: DeleteTitlePageResponse::class))
            ),
            new OA\Response(response: 404, description: 'Not Found'),
        ]
    )]
    public function delete(
        Project   $project,
        TitlePage $titlePage,
    ): JsonResponse {
        $response = $this->titlePageService->delete($titlePage);
        return $this->json($response, Response::HTTP_OK);
    }
}
