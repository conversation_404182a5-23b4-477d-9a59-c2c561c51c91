<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api\FrontMatter;

use App\Dto\ErrorResponseDto;
use App\Dto\Xml2\FrontMatter\CreatePublisherNoteRequest;
use App\Dto\Xml2\FrontMatter\DeletePublisherNoteResponse;
use App\Dto\Xml2\FrontMatter\UpdatePublisherNoteRequest;
use App\Entity\CodeBook\PublisherNote;
use App\Entity\Project;
use App\Serializer\Dto\Book\PublisherNoteDto;
use App\Service\Xml2\FrontMatter\PublisherNoteService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}/front-matter/publisher-note', format: 'json')]
#[OA\Tag(name: 'Front Matter')]
#[Nelmio\Security(name: 'Bearer')]
class PublisherNoteController extends AbstractController
{
    public function __construct(private readonly PublisherNoteService $publisherNoteService)
    {
    }

    #[Route(path: '', methods: ['POST'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Post(
        summary: 'Create Publisher Note',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreatePublisherNoteRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 201,
                description: 'Publisher Note created',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: PublisherNoteDto::class))
            ),
            new OA\Response(
                response: 400,
                description: 'Creation error',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function create(
        Project                    $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        CreatePublisherNoteRequest $request,
    ): JsonResponse {
        $publisherNote = $this->publisherNoteService->create($project, $request);
        return $this->json($publisherNote, Response::HTTP_CREATED);
    }

    #[Route(path: '/{nodeId:publisherNote}', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Get Publisher Note',
        responses: [
            new OA\Response(
                response: 200,
                description: 'Get Publisher Note',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: PublisherNoteDto::class))
            ),
            new OA\Response(response: 404, description: 'Node not found'),
        ]
    )]
    public function read(
        Project       $project,
        PublisherNote $publisherNote,
    ): JsonResponse {
        return $this->json($publisherNote, Response::HTTP_OK);
    }

    #[Route(path: '/{nodeId:publisherNote}', methods: ['PUT'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Put(
        summary: 'Update Publisher Note',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdatePublisherNoteRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 202,
                description: 'Publisher Note updated',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: PublisherNoteDto::class))
            ),
            new OA\Response(
                response: 400,
                description: 'Update error',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function update(
        Project                    $project,
        PublisherNote              $publisherNote,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdatePublisherNoteRequest $request,
    ): JsonResponse {
        $this->publisherNoteService->update($publisherNote, $request);
        return $this->json($publisherNote, Response::HTTP_OK);
    }

    #[Route(path: '/{nodeId:publisherNote}', methods: ['DELETE'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Delete(
        summary: 'Delete Publisher Note',
        responses: [
            new OA\Response(
                response: 202,
                description: 'Publisher Note deleted',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: DeletePublisherNoteResponse::class))
            ),
            new OA\Response(response: 404, description: 'Node not found'),
        ]
    )]
    public function delete(
        Project       $project,
        PublisherNote $publisherNote,
    ): JsonResponse {
        $response = $this->publisherNoteService->delete($publisherNote);
        return $this->json($response, Response::HTTP_OK);
    }
}
