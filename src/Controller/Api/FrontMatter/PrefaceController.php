<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api\FrontMatter;

use App\Dto\ErrorResponseDto;
use App\Dto\Xml2\FrontMatter\CreatePrefaceRequest;
use App\Dto\Xml2\FrontMatter\DeletePrefaceResponse;
use App\Dto\Xml2\FrontMatter\UpdatePrefaceRequest;
use App\Entity\CodeBook\Preface;
use App\Entity\Project;
use App\Serializer\Dto\Book\PrefaceDto;
use App\Service\Xml2\FrontMatter\PrefaceService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}/front-matter/preface', format: 'json')]
#[OA\Tag(name: 'Front Matter')]
#[Nelmio\Security(name: 'Bearer')]
class PrefaceController extends AbstractController
{
    public function __construct(private readonly PrefaceService $prefaceService)
    {
    }

    #[Route(path: '', methods: ['POST'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Post(
        summary: 'Create Preface',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreatePrefaceRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 201,
                description: 'Preface created',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: PrefaceDto::class))
            ),
            new OA\Response(
                response: 400,
                description: 'Creation error',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function create(
        Project              $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        CreatePrefaceRequest $request,
    ): JsonResponse {
        $preface = $this->prefaceService->create($project, $request);
        return $this->json($preface, Response::HTTP_CREATED);
    }

    #[Route(path: '/{nodeId:preface}', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Get Preface',
        responses: [
            new OA\Response(
                response: 200,
                description: 'Get Preface',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: PrefaceDto::class))
            ),
            new OA\Response(response: 404, description: 'Node not found'),
        ]
    )]
    public function read(
        Project $project,
        Preface $preface,
    ): JsonResponse {
        return $this->json($preface, Response::HTTP_OK);
    }

    #[Route(path: '/{nodeId:preface}', methods: ['PUT'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Put(
        summary: 'Update Preface',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdatePrefaceRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 202,
                description: 'Preface updated',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: PrefaceDto::class))
            ),
            new OA\Response(
                response: 400,
                description: 'Update error',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function update(
        Project              $project,
        Preface              $preface,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdatePrefaceRequest $request,
    ): JsonResponse {
        $this->prefaceService->update($preface, $request);
        return $this->json($preface, Response::HTTP_OK);
    }

    #[Route(path: '/{nodeId:preface}', methods: ['DELETE'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Delete(
        summary: 'Delete Preface',
        responses: [
            new OA\Response(
                response: 202,
                description: 'Preface deleted',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: DeletePrefaceResponse::class))
            ),
            new OA\Response(response: 404, description: 'Node not found'),
        ]
    )]
    public function delete(
        Project $project,
        Preface $preface,
    ): JsonResponse {
        $response = $this->prefaceService->delete($preface);
        return $this->json($response, Response::HTTP_OK);
    }
}
