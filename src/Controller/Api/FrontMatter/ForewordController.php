<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api\FrontMatter;

use App\Dto\ErrorResponseDto;
use App\Dto\Xml2\FrontMatter\CreateForewordRequest;
use App\Dto\Xml2\FrontMatter\DeleteForewordResponse;
use App\Dto\Xml2\FrontMatter\UpdateForewordRequest;
use App\Entity\CodeBook\Foreword;
use App\Entity\Project;
use App\Serializer\Dto\Book\ForewordDto;
use App\Service\Xml2\FrontMatter\ForewordService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}/front-matter/foreword', format: 'json')]
#[OA\Tag(name: 'Front Matter')]
#[Nelmio\Security(name: 'Bearer')]
class ForewordController extends AbstractController
{
    public function __construct(private readonly ForewordService $forewordService)
    {
    }

    #[Route(path: '', methods: ['POST'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Post(
        summary: 'Create Foreword',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateForewordRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 201,
                description: 'Foreword created',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ForewordDto::class))
            ),
            new OA\Response(
                response: 400,
                description: 'Creation error',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function create(
        Project               $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        CreateForewordRequest $request,
    ): JsonResponse {
        $foreword = $this->forewordService->create($project, $request);
        return $this->json($foreword, Response::HTTP_CREATED);
    }

    #[Route(path: '/{nodeId:foreword}', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Get Foreword',
        responses: [
            new OA\Response(
                response: 200,
                description: 'Get Foreword',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ForewordDto::class))
            ),
            new OA\Response(response: 404, description: 'Node not found'),
        ]
    )]
    public function read(
        Project  $project,
        Foreword $foreword,
    ): JsonResponse {
        return $this->json($foreword, Response::HTTP_OK);
    }

    #[Route(path: '/{nodeId:foreword}', methods: ['PUT'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Put(
        summary: 'Update Foreword',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateForewordRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 202,
                description: 'Foreword updated',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ForewordDto::class))
            ),
            new OA\Response(
                response: 400,
                description: 'Update error',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function update(
        Project               $project,
        Foreword              $foreword,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdateForewordRequest $request,
    ): JsonResponse {
        $this->forewordService->update($foreword, $request);
        return $this->json($foreword, Response::HTTP_OK);
    }

    #[Route(path: '/{nodeId:foreword}', methods: ['DELETE'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Delete(
        summary: 'Delete Foreword',
        responses: [
            new OA\Response(
                response: 202,
                description: 'Foreword deleted',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: DeleteForewordResponse::class))
            ),
            new OA\Response(response: 404, description: 'Node not found'),
        ]
    )]
    public function delete(
        Project  $project,
        Foreword $foreword,
    ): JsonResponse {
        $response = $this->forewordService->delete($foreword);
        return $this->json($response, Response::HTTP_OK);
    }
}
