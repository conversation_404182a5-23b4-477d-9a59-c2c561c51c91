<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api\FrontMatter;

use App\Dto\ErrorResponseDto;
use App\Dto\Xml2\FrontMatter\CreateCopyrightPageRequest;
use App\Dto\Xml2\FrontMatter\DeleteCopyrightPageResponse;
use App\Dto\Xml2\FrontMatter\UpdateCopyrightPageRequest;
use App\Entity\CodeBook\CopyrightPage;
use App\Entity\Project;
use App\Serializer\Dto\Book\CopyrightPageDto;
use App\Service\Xml2\FrontMatter\CopyrightPageService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}/front-matter/copyright')]
#[OA\Tag(name: 'Front Matter')]
#[Nelmio\Security(name: 'Bearer')]
class CopyrightPageController extends AbstractController
{
    public function __construct(
        private readonly CopyrightPageService $copyrightPageService
    ) {
    }

    #[Route(path: '', methods: ['POST'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Post(
        summary: 'Create Copyright Page',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateCopyrightPageRequest::class))
        ),
        responses: [
            new OA\Response(
                response: Response::HTTP_CREATED,
                description: 'Copyright Page created',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: CopyrightPageDto::class))
            ),
            new OA\Response(
                response: 400,
                description: 'Creation error',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function create(
        Project                    $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        CreateCopyrightPageRequest $request,
    ): JsonResponse {
        $copyrightPage = $this->copyrightPageService->create($project, $request);
        return $this->json($copyrightPage, Response::HTTP_CREATED);
    }

    #[Route(path: '/{nodeId:copyrightPage}', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Get Copyright Page',
        responses: [
            new OA\Response(
                response: Response::HTTP_OK,
                description: 'Get Copyright Page',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: CopyrightPageDto::class))
            ),
            new OA\Response(response: 404, description: 'Node not found'),
        ]
    )]
    public function read(
        Project       $project,
        CopyrightPage $copyrightPage,
    ): JsonResponse {
        return $this->json($copyrightPage, Response::HTTP_OK);
    }

    #[Route(path: '/{nodeId:copyrightPage}', methods: ['PUT'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Put(
        summary: 'Update Copyright Page',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateCopyrightPageRequest::class))
        ),
        responses: [
            new OA\Response(
                response: Response::HTTP_OK,
                description: 'Copyright Page updated',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: CopyrightPageDto::class))
            ),
            new OA\Response(
                response: 400,
                description: 'Update error',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function update(
        Project                    $project,
        CopyrightPage              $copyrightPage,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdateCopyrightPageRequest $request,
    ): JsonResponse {
        $this->copyrightPageService->update($copyrightPage, $request);
        return $this->json($copyrightPage, Response::HTTP_OK);
    }

    #[Route(path: '/{nodeId:copyrightPage}', methods: ['DELETE'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Delete(
        summary: 'Delete Copyright Page',
        responses: [
            new OA\Response(
                response: Response::HTTP_OK,
                description: 'Copyright Page deleted',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: DeleteCopyrightPageResponse::class))
            ),
            new OA\Response(response: 404, description: 'Node not found'),
        ]
    )]
    public function delete(
        Project       $project,
        CopyrightPage $copyrightPage,
    ): JsonResponse {
        $response = $this->copyrightPageService->delete($copyrightPage);
        return $this->json($response, Response::HTTP_OK);
    }
}
