<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\ErrorResponseDto;
use App\Dto\Xml2\Volume\UpdateVolumeRequest;
use App\Dto\Xml2\Volume\VolumeResponse;
use App\Entity\CodeBook\Volume;
use App\Entity\Project;
use App\Service\Xml2\VolumeService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}/volume', format: 'json')]
#[OA\Tag(name: 'Volume')]
#[Nelmio\Security(name: 'Bearer')]
class VolumeController extends AbstractController
{
    public function __construct(private readonly VolumeService $volumeService)
    {
    }

    #[IsGranted('VIEW_PROJECT', 'project')]
    #[Route(path: '/{nodeId:volume}', methods: ['GET'])]
    #[Route(path: '/{ulid:volume}', methods: ['GET'])]
    #[OA\Get(
        summary: 'Get Volume',
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: VolumeResponse::class))
            ),
            new OA\Response(response: 404, description: 'Not Found'),
        ]
    )]
    public function read(
        Project $project,
        Volume  $volume,
    ): JsonResponse {
        return $this->json(new VolumeResponse($volume), Response::HTTP_OK);
    }

    #[IsGranted('EDIT_PROJECT', 'project')]
    #[Route(path: '/{nodeId:volume}', methods: ['PUT'])]
    #[Route(path: '/{ulid:volume}', methods: ['PUT'])]
    #[OA\Put(
        summary: 'Update Volume',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateVolumeRequest::class))
        ),
        responses: [
            new OA\Response(
                response: Response::HTTP_OK,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: VolumeResponse::class))
            ),
            new OA\Response(
                response: Response::HTTP_BAD_REQUEST,
                description: 'Bad Request',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
            new OA\Response(response: Response::HTTP_NOT_FOUND, description: 'Not Found'),
        ]
    )]
    public function update(
        Project             $project,
        Volume              $volume,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdateVolumeRequest $request,
    ): JsonResponse {
        $this->volumeService->update($volume, $request);
        return $this->json(new VolumeResponse($volume), Response::HTTP_OK);
    }
}
