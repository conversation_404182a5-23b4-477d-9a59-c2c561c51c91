<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\Xml2\NoteAttachment\CreateNoteAttachmentRequest;
use App\Dto\Xml2\NoteAttachment\CreateNoteAttachmentResponse;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\Project;
use App\Service\Xml2\NoteAttachmentService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;

#[Route(path: '/api/v2/books/{shortCode:project}/attachment')]
#[OA\Tag(name: 'Attachments')]
#[Nelmio\Security(name: 'Bearer')]
class NoteAttachmentController extends AbstractController
{
    public function __construct(private readonly NoteAttachmentService $noteAttachmentService)
    {
    }

    #[Route(path: '/{nodeId:node}', methods: ['POST'])]
    #[OA\Post(
        summary: 'Upload attachment',
        requestBody: new OA\RequestBody(
            description: 'Create a new attachment',
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateNoteAttachmentRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: 'Attachment was created',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateNoteAttachmentResponse::class))
            ),
        ]
    )]
    public function create(
        Project                     $project,
        AbstractCodeBookNode        $node,
        #[MapRequestPayload(acceptFormat: 'json')]
        CreateNoteAttachmentRequest $request,
    ): JsonResponse {
        return $this->json($this->noteAttachmentService->create($request, $project->getShortCode(), $node));
    }

    #[Route(path: '/{noteAttachmentId}', methods: ['DELETE'])]
    #[OA\Delete(
        summary: 'Delete an attachment',
        parameters: [
            new OA\Parameter(
                name: 'noteAttachmentId',
                description: 'Primary-key of the attachment to delete',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'integer')
            ),
        ],
        responses: [
            new OA\Response(
                response: 204,
                description: 'Attachment deleted'
            ),
        ]
    )]
    public function delete(int $noteAttachmentId): JsonResponse
    {
        $this->noteAttachmentService->delete($noteAttachmentId);
        return $this->json(null, Response::HTTP_NO_CONTENT);
    }
}
