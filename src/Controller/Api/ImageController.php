<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\Xml2\Image\CreateImageRequest;
use App\Dto\Xml2\Image\CreateImageResponse;
use App\Service\Xml2\ImageService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;

#[Route(path: '/api/v2/images', format: 'json')]
#[OA\Tag(name: 'Images')]
#[Nelmio\Security(name: 'Bearer')]
class ImageController extends AbstractController
{
    public function __construct(private readonly ImageService $imageService)
    {
    }

    #[Route(path: '', methods: ['POST'])]
    #[OA\Post(
        summary: 'Upload image',
        requestBody: new OA\RequestBody(
            description: 'Create a new Chapter',
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateImageRequest::class))
        ),
        responses: [
            new OA\Response(
                response: Response::HTTP_CREATED,
                description: 'CREATED',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateImageResponse::class))
            ),
        ]
    )]
    public function create(
        #[MapRequestPayload(acceptFormat: 'json')]
        CreateImageRequest $request,
    ): JsonResponse {
        $response = $this->imageService->create($request);
        return $this->json($response, Response::HTTP_CREATED);
    }
}
