<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api\Qr;

use App\Dto\Xml2\Qr\CreateQrRequest;
use App\Dto\Xml2\Qr\CreateQrResponse;
use App\Dto\Xml2\Qr\UpdateQrRequest;
use App\Dto\Xml2\Qr\UpdateQrResponse;
use App\Service\Qr\QrService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;

#[Route(path: '/api/v2/qr')]
#[OA\Tag(name: 'QR')]
#[Nelmio\Security(name: 'Bearer')]
class QrController extends AbstractController
{
    private QrService $qrService;

    public function __construct(QrService $qrService)
    {
        $this->qrService = $qrService;
    }

    #[Route(path: '', methods: ['POST'])]
    #[OA\Post(
        summary: 'Create QR Code',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateQrRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 201,
                description: 'Created',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateQrResponse::class))
            ),
        ]
    )]
    public function create(#[MapRequestPayload(acceptFormat: 'json')] CreateQrRequest $qrRequest): JsonResponse
    {
        return $this->json($this->qrService->add($qrRequest), Response::HTTP_CREATED);
    }

    #[Route(path: '/{filename}', methods: ['POST'])]
    #[Route(path: '', methods: ['PUT'])]
    #[OA\Put(
        summary: 'Update QR Code',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateQrRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateQrResponse::class))
            ),
        ]
    )]
    #[OA\Post(
        summary: 'Update QR Code',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateQrRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 202,
                description: 'Accepted',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateQrResponse::class))
            ),
        ]
    )]
    public function update(#[MapRequestPayload(acceptFormat: 'json')] UpdateQrRequest $request): JsonResponse
    {
        // Controller returns 202 for both routes by design; adjust if you want 200 for PUT.
        return $this->json($this->qrService->update($request), Response::HTTP_ACCEPTED);
    }
}
