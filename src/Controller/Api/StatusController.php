<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\ErrorResponseDto;
use App\Dto\Xml2\Status\ChapterStatusRequest;
use App\Dto\Xml2\Status\SectionStatusRequest;
use App\Entity\Project;
use App\Exception\ApiException;
use App\Service\Xml2\StatusService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}/chapters/{chapterId}', format: 'json')]
#[OA\Tag(name: 'Status')]
#[Nelmio\Security(name: 'Bearer')]
#[IsGranted('EDIT_PROJECT', 'project')]
class StatusController extends AbstractController
{
    public function __construct(private readonly StatusService $statusService)
    {
    }

    #[Route(path: '/fast-track', name: 'api_xml2_chapter_fast_track', methods: ['PUT'])]
    #[OA\Put(
        summary: 'Fast-track a chapter',
        responses: [
            new OA\Response(
                response: 404,
                description: 'Error updating status.',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function chapterFastTrack(
        Project $project,
        string  $chapterId,
    ): JsonResponse {
        try {
            return $this->json($this->statusService->chapterFastTrack($chapterId));
        } catch (ApiException $e) {
            return $this->json(new ErrorResponseDto($e->getMessage(), $e->getCode()), $e->getCode());
        }
    }

    #[Route(path: '/update-status', name: 'api_xml2_chapter_status_update', methods: ['PUT'])]
    #[OA\Put(
        summary: 'Update chapter status',
        requestBody: new OA\RequestBody(
            request: 'Sections Status',
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: ChapterStatusRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 404,
                description: 'Error updating status.',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function updateChapterStatus(
        Project              $project,
        string               $chapterId,
        #[MapRequestPayload(acceptFormat: 'json')]
        ChapterStatusRequest $request,
    ): JsonResponse {
        try {
            $user = $this->getUser();
            return $this->json($this->statusService->chapterStatusUpdate($project, $chapterId, $request, $user), Response::HTTP_ACCEPTED);
        } catch (ApiException $e) {
            return $this->json(new ErrorResponseDto($e->getMessage(), $e->getCode()), $e->getCode());
        }
    }

    #[Route(path: '/update-sections-status', name: 'api_xml2_section_update_status', methods: ['PUT'])]
    #[OA\Put(
        summary: 'Update sections status',
        requestBody: new OA\RequestBody(
            request: 'Sections Status',
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: SectionStatusRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 404,
                description: 'Error updating status.',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function updateSectionStatus(
        Project              $project,
        string               $chapterId,
        #[MapRequestPayload(acceptFormat: 'json')]
        SectionStatusRequest $request,
    ): JsonResponse {
        try {
            return $this->json($this->statusService->sectionStatusUpdate($chapterId, $request), Response::HTTP_ACCEPTED);
        } catch (ApiException $e) {
            return $this->json(new ErrorResponseDto($e->getMessage(), $e->getCode()), $e->getCode());
        }
    }
}
