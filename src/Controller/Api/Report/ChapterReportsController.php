<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api\Report;

use App\Dto\Xml2\Reports\Chapter\GetChapterPdfRequest;
use App\Dto\Xml2\Reports\Chapter\GetChapterXmlExportRequest;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\Project;
use App\Service\Xml2\ReportService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/reports/{shortCode:project}')]
#[OA\Tag(name: 'Report Queue')]
#[Nelmio\Security(name: 'Bearer')]
#[IsGranted('VIEW_PROJECT', 'project')]
class ChapterReportsController extends AbstractController
{
    public function __construct(private readonly ReportService $reportService)
    {
    }

    #[Route(path: '/chapters/{nodeId:node}/code-changes.pdf', methods: ['GET'])]
//    #[OA\Get(
//        summary: 'Get Chapter Code Changes PDF',
//        requestBody: new OA\RequestBody(
//            request: 'Get Chapter Pdf Report',
//            required: true,
//            content: new OA\JsonContent(ref: new Nelmio\Model(type: GetChapterPdfRequest::class))
//        ),
//        responses: [
//            new OA\Response(response: 200, description: 'PDF Download'),
//        ]
//    )]
    public function getCodeChanges(
        Project              $project,
        AbstractCodeBookNode $node,
        #[MapQueryString]
        GetChapterPdfRequest $request,
    ): Response {
        return $this->reportService->codeChangesReport($node, $request);
    }

    #[Route(path: '/chapters/{nodeId:node}/export.zip', methods: ['GET'])]
//    #[OA\Get(
//        summary: 'Get Chapter XML Export (ZIP)',
//        requestBody: new OA\RequestBody(
//            request: 'Get Chapter Pdf Report',
//            required: true,
//            content: new OA\JsonContent(ref: new Nelmio\Model(type: GetChapterXmlExportRequest::class))
//        ),
//        responses: [
//            new OA\Response(response: 200, description: 'ZIP Download'),
//        ]
//    )]
    public function getExport(
        Project                    $project,
        AbstractCodeBookNode       $node,
        #[MapQueryString]
        GetChapterXmlExportRequest $request,
    ): Response {
        return $this->reportService->chapterXmlExport($node, $request);
    }
}
