<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api\Report;

use App\Entity\CodeBook\Chapter;
use App\Entity\Project;
use App\Service\Xml2\Report\ErrataReportService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/reports/{shortCode:project}')]
#[OA\Tag(name: 'Report')]
#[Nelmio\Security(name: 'Bearer')]
#[IsGranted('VIEW_PROJECT', 'project')]
class ErrataReportController extends AbstractController
{
    public function __construct(private readonly ErrataReportService $errataReportService)
    {
    }

    #[Route(path: '/errata-report.pdf', methods: ['GET'])]
    #[OA\Get(
        summary: 'Generate Project Errata Report',
        responses: [
            new OA\Response(response: 200, description: 'PDF Download'),
        ]
    )]
    public function projectLevel(
        Project $project,
    ): Response {
        return $this->errataReportService->getErrataReport($project, $project->getCodeBook());
    }

    #[Route(path: '/chapters/{nodeId:chapter}/chapter-errata-report.pdf', methods: ['GET'])]
    #[OA\Get(
        summary: 'Generate Chapter Errata Report',
        responses: [
            new OA\Response(response: 200, description: 'PDF Download'),
        ]
    )]
    public function chapterLevel(
        Project $project,
        Chapter $chapter,
    ): Response {
        return $this->errataReportService->getErrataReport($project, $chapter);
    }
}
