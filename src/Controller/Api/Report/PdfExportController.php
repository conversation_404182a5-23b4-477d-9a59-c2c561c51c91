<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api\Report;

use App\Dto\Project\Report\CreateExportStatusResponse;
use App\Dto\Project\Report\Pdf\CreatePdfExportRequest;
use App\Dto\Project\Report\Pdf\CreatePdfExportResponse;
use App\Entity\Project;
use App\Entity\User\User;
use App\Message\Project\BookPDFExportMessage;
use App\Service\JobStatusService;
use App\Service\Xml2\ReportService;
use Doctrine\ORM\EntityManagerInterface;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/reports/{shortCode:project}/export-pdf')]
#[OA\Tag(name: 'Report Queue')]
#[Nelmio\Security(name: 'Bearer')]
class PdfExportController extends AbstractController
{
    public function __construct(
        private readonly MessageBusInterface    $messageBus,
        private readonly ReportService          $reportService,
        private readonly EntityManagerInterface $entityManager
    ) {
    }

    #[Route(path: '', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
//    #[OA\Get(
//        summary: 'Request a PDF Export',
//        parameters: [
//            new OA\Parameter(
//                name: 'clean',
//                description: 'Include redline text',
//                in: 'query',
//                required: false,
//                schema: new OA\Schema(type: 'boolean')
//            ),
//        ],
//        responses: [
//            new OA\Response(
//                response: 201,
//                description: 'PDF Export requested',
//                content: new OA\JsonContent(ref: CreatePdfExportResponse::class)
//            ),
//        ]
//    )]
    public function getBookPdf(
        Project                $project,
        #[MapQueryString]
        CreatePdfExportRequest $request,
    ): JsonResponse {
        $user = $this->entityManager->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getEmail()]);
        $username = trim(str_replace(' ', '_', (string) $user));

        $message = new BookPDFExportMessage($project, $username, $request->clean);
        $this->messageBus->dispatch($message);

        return $this->json(new CreatePdfExportResponse(), Response::HTTP_CREATED);
    }

    #[Route(path: '/status', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Get PDF Export status',
        responses: [
            new OA\Response(
                response: 200,
                description: 'PDF Export status',
                content: new OA\JsonContent(ref: CreateExportStatusResponse::class)
            ),
        ]
    )]
    public function status(
        Project $project,
    ): JsonResponse {
        $data = $this->reportService->getStatus(
            $project->getShortCode(),
            JobStatusService::BOOK_PDF_EXPORT_TYPE,
            'book-export-pdf-xml2-clean',
            'book-export-pdf-xml2'
        );
        $response = CreateExportStatusResponse::createFromResponse($data);

        return $this->json($response, Response::HTTP_OK);
    }
}
