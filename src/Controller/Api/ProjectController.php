<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\ErrorResponseDto;
use App\Dto\Project\DeleteProjectResponse;
use App\Dto\Xml2\Project\CreateProjectRequest;
use App\Dto\Xml2\Project\UpdateProjectRequest;
use App\Entity\Project;
use App\Entity\User\User;
use App\Serializer\Dto\Project\ProjectDto;
use App\Serializer\Dto\Project\ProjectTypeDto;
use App\Serializer\Dto\Project\VersionTypeDto;
use App\Service\Xml2\ProjectService;
use Doctrine\ORM\EntityManagerInterface;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/projects')]
#[OA\Tag(name: 'Project')]
#[Nelmio\Security(name: 'Bearer')]
class ProjectController extends AbstractController
{
    public function __construct(
        private readonly ProjectService         $projectService,
        private readonly EntityManagerInterface $entityManager
    ) {
    }

    #[IsGranted('ROLE_ADMIN')]
    #[Route(path: '', methods: ['POST'])]
    #[OA\Post(
        summary: 'Create a new project',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateProjectRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 201,
                description: 'Created',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ProjectDto::class))
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function create(
        #[MapRequestPayload(acceptFormat: 'json')]
        CreateProjectRequest $request
    ): JsonResponse {
        $project = $this->projectService->create($request);
        return $this->json($project, Response::HTTP_CREATED, [], ['groups' => ['project:read']]);
    }

    #[IsGranted('VIEW_PROJECT', 'project')]
    #[Route(path: '/{id:project<\d+>}', methods: ['GET'])]
    #[OA\Get(
        summary: 'Get project details',
        responses: [
            new OA\Response(
                response: 202,
                description: 'Project details',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ProjectDto::class))
            ),
            new OA\Response(
                response: 404,
                description: 'Not Found',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function read(Project $project): JsonResponse
    {
        return $this->json($project, Response::HTTP_OK, [], ['groups' => ['project:read']]);
    }

    #[IsGranted('ROLE_ADMIN')]
    #[Route(path: '/{id:project<\d+>}', methods: ['PUT'])]
    #[OA\Put(
        summary: 'Update Project details',
        requestBody: new OA\RequestBody(
            request: 'Update Project',
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateProjectRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: 'Project updated successfully',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ProjectDto::class))
            ),
            new OA\Response(
                response: 400,
                description: 'Update error',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function update(
        Project              $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdateProjectRequest $request
    ): JsonResponse {
        $this->projectService->update($project, $request);
        return $this->json($project, Response::HTTP_OK, [], ['groups' => ['project:read']]);
    }

    #[IsGranted('ROLE_ADMIN')]
    #[Route(path: '/{id:project<\d+>}', methods: ['DELETE'])]
    #[OA\Delete(
        summary: 'Delete project',
        responses: [
            new OA\Response(
                response: 200,
                description: 'Delete Project',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: DeleteProjectResponse::class))
            ),
        ]
    )]
    public function delete(Project $project, #[CurrentUser] User $user): JsonResponse
    {
        return $this->json($this->projectService->delete($project, $user));
    }

    #[Route(path: '', methods: ['GET'])]
    #[OA\Get(
        summary: 'List projects available to the user',
        responses: [
            new OA\Response(
                response: 200,
                description: 'Project List',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: new Nelmio\Model(type: ProjectDto::class))
                )
            ),
        ]
    )]
    public function list(#[CurrentUser] User $user): JsonResponse
    {
        return $this->json($this->projectService->list($user));
    }

    #[Route(path: '/active', methods: ['GET'])]
    #[OA\Get(
        summary: 'List active projects available to the user',
        responses: [
            new OA\Response(
                response: 200,
                description: 'Project Active List',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: new Nelmio\Model(type: ProjectDto::class))
                )
            ),
        ]
    )]
    public function listActives(#[CurrentUser] User $user): JsonResponse
    {
        return $this->json($this->projectService->listActiveProjects($user));
    }

    #[IsGranted('ROLE_ADMIN')]
    #[Route(path: '/version-types', methods: ['GET'])]
    #[OA\Get(
        summary: 'Get all Version Types',
        responses: [
            new OA\Response(
                response: 200,
                description: 'List of version types',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: new Nelmio\Model(type: VersionTypeDto::class))
                )
            ),
        ]
    )]
    public function getVersionTypes(): JsonResponse
    {
        return $this->json($this->projectService->getProjectVersionTypes());
    }

    #[IsGranted('ROLE_ADMIN')]
    #[Route(path: '/project-types', methods: ['GET'])]
    #[OA\Get(
        summary: 'Get all Project Types',
        responses: [
            new OA\Response(
                response: 200,
                description: 'List of project types',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: new Nelmio\Model(type: ProjectTypeDto::class))
                )
            ),
        ]
    )]
    public function getProjectTypes(): JsonResponse
    {
        return $this->json($this->projectService->getProjectTypes());
    }

    #[IsGranted('ROLE_ADMIN')]
    #[Route(path: '/{id:project<\d+>}/set-project-status', methods: ['PUT'])]
    public function setStatus(Request $request, Project $project): JsonResponse
    {
        $status = $request->request->getBoolean('active');

        if ($status === null) {
            return $this->json(
                [
                    'success' => false,
                    'error'   => 'Unable to close the project.',
                ],
                Response::HTTP_BAD_REQUEST
            );
        }

        try {
            $project->setActive($status);
            $this->entityManager->flush();

            return $this->json(null, Response::HTTP_NO_CONTENT);
        } catch (\Throwable $e) {
            return $this->json([
                'success' => true,
                'message' => 'Failed to close the project',
            ], Response::HTTP_BAD_REQUEST);
        }
    }
}
