<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\ErrorResponseDto;
use App\Dto\Xml2\DeleteNodeRequest;
use App\Dto\Xml2\Table\CreateTableRequest;
use App\Dto\Xml2\Table\DeleteTableResponse;
use App\Dto\Xml2\Table\UpdateTableRequest;
use App\Entity\CodeBook\Table;
use App\Entity\Project;
use App\Serializer\Dto\Book\TableDto;
use App\Service\Xml2\TableService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}', format: 'json')]
#[OA\Tag(name: 'Table')]
#[Nelmio\Security(name: 'Bearer')]
class TableController extends AbstractController
{
    public function __construct(private readonly TableService $tableService)
    {
    }

    #[IsGranted('VIEW_PROJECT', 'project')]
    #[Route(path: '/tables', methods: ['GET'])]
    #[OA\Get(
        summary: 'List all Tables from Project',
        responses: [
            new OA\Response(
                response: 200,
                description: 'List of Tables',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: new Nelmio\Model(type: TableDto::class))
                )
            ),
        ]
    )]
    public function index(
        Project $project,
    ): JsonResponse {
        $list = $this->tableService->list($project);
        return $this->json($list, Response::HTTP_OK);
    }

    #[IsGranted('EDIT_PROJECT', 'project')]
    #[Route(path: '/tables', methods: ['POST'])]
    #[Route(path: '/chapters/{chapterId}/tables', methods: ['POST'])]
    #[OA\Post(
        summary: 'Create new Table',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateTableRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 201,
                description: 'Table created',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: TableDto::class))
            ),
            new OA\Response(response: 400, description: 'Create error'),
        ]
    )]
    public function create(
        Project            $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        CreateTableRequest $request,
        string             $chapterId = '',
    ): JsonResponse {
        $table = $this->tableService->create($project, $request);
        return $this->json($table, Response::HTTP_CREATED);
    }

    #[IsGranted('VIEW_PROJECT', 'project')]
    #[Route(path: '/tables/{nodeId:table}', methods: ['GET'])]
    #[Route(path: '/chapters/{chapterId}/tables/{nodeId:table}', methods: ['GET'])]
    #[OA\Get(
        summary: 'Get Table',
        responses: [
            new OA\Response(
                response: 200,
                description: 'Get Table',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: TableDto::class))
            ),
            new OA\Response(response: 404, description: 'Table not found'),
        ]
    )]
    public function read(
        Project $project,
        Table   $table,
    ): JsonResponse {
        return $this->json($table, Response::HTTP_OK);
    }

    #[IsGranted('EDIT_PROJECT', 'project')]
    #[Route(path: '/tables/{nodeId:table}', methods: ['PUT'])]
    #[Route(path: '/chapters/{chapterId}/tables/{nodeId:table}', methods: ['PUT'])]
    #[OA\Put(
        summary: 'Update Table',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateTableRequest::class))
        ),
        responses: [
            new OA\Response(
                response: Response::HTTP_OK,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: TableDto::class))
            ),
            new OA\Response(response: 404, description: 'Update error'),
        ]
    )]
    public function update(
        Project            $project,
        Table              $table,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdateTableRequest $request,
        string             $chapterId = '',
    ): JsonResponse {
        $this->tableService->update($project, $table, $request);
        return $this->json($table, Response::HTTP_OK);
    }

    #[IsGranted('EDIT_PROJECT', 'project')]
    #[Route(path: '/tables/{nodeId:table}', methods: ['DELETE'])]
    #[Route(path: '/chapters/{chapterId}/tables/{nodeId:table}', methods: ['DELETE'])]
    #[OA\Delete(
        summary: 'Delete Table',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: DeleteNodeRequest::class))
        ),
        responses: [
            new OA\Response(
                response: 202,
                description: 'Table deleted',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: DeleteTableResponse::class))
            ),
            new OA\Response(
                response: 400,
                description: 'Delete error',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    public function delete(
        Project           $project,
        Table             $table,
        #[MapRequestPayload(acceptFormat: 'json')]
        DeleteNodeRequest $request,
    ): JsonResponse {
        $response = $this->tableService->delete($table, $request);
        return $this->json($response, Response::HTTP_OK);
    }
}
