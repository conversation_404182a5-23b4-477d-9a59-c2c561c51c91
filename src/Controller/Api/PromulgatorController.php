<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\ErrorResponseDto;
use App\Dto\Xml2\Promulgator\AddStandardToPromulgatorRequest;
use App\Dto\Xml2\Promulgator\AddStandardToPromulgatorResponse;
use App\Dto\Xml2\Promulgator\CreatePromulgatorRequest;
use App\Dto\Xml2\Promulgator\MovePromulgatorRequest;
use App\Dto\Xml2\Promulgator\MovePromulgatorResponse;
use App\Dto\Xml2\Promulgator\UpdatePromulgatorRequest;
use App\Entity\CodeBook\Promulgator;
use App\Entity\Project;
use App\Serializer\Dto\Book\PromulgatorDto;
use App\Service\Xml2\PromulgatorService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode:project}')]
#[OA\Tag(name: 'Promulgator')]
#[Nelmio\Security(name: 'Bearer')]
#[OA\Parameter(
    name: 'shortCode',
    description: 'The shortcode of the Project',
    in: 'path',
    required: true,
    schema: new OA\Schema(type: 'string')
)]
class PromulgatorController extends AbstractController
{
    public function __construct(
        private readonly PromulgatorService $promulgatorService
    ) {
    }

    #[Route(path: '/promulgators', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Retrieves a list of all promulgators',
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: new Nelmio\Model(type: PromulgatorDto::class))
                )
            ),
        ]
    )]
    public function list(Project $project): JsonResponse
    {
        return $this->json($this->promulgatorService->list($project), Response::HTTP_OK);
    }

    #[Route(path: '/promulgators', methods: ['POST'])]
    #[Route(path: '/chapters/{chapterId}/promulgator', methods: ['POST'])]
    #[Route(path: '/chapters/{chapterId}/promulgators', methods: ['POST'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Post(
        summary: 'Create a new Promulgator',
        requestBody: new OA\RequestBody(
            required: true,
            content: new Nelmio\Model(type: CreatePromulgatorRequest::class)
        ),
        responses: [
            new OA\Response(response: 201, description: 'Created', content: new Nelmio\Model(type: PromulgatorDto::class)),
            new OA\Response(response: 400, description: 'Bad Request', content: new Nelmio\Model(type: ErrorResponseDto::class)),
        ]
    )]
    public function create(
        Project                  $project,
        #[MapRequestPayload(acceptFormat: 'json')]
        CreatePromulgatorRequest $request,
        string                   $chapterId = '',
    ): JsonResponse {
        $promulgator = $this->promulgatorService->create($project, $request);
        return $this->json($promulgator, Response::HTTP_CREATED);
    }

    #[Route(path: '/promulgators/{nodeId:promulgator}', methods: ['GET'])]
    #[Route(path: '/chapters/{chapterId}/promulgators/{nodeId:promulgator}', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Get a Promulgator',
        responses: [
            new OA\Response(response: 200, description: 'OK', content: new Nelmio\Model(type: PromulgatorDto::class)),
            new OA\Response(response: 404, description: 'Promulgator not found'),
        ]
    )]
    public function read(Project $project, Promulgator $promulgator): JsonResponse
    {
        return $this->json($promulgator, Response::HTTP_OK);
    }

    #[Route(path: '/promulgators/{nodeId:promulgator}', methods: ['PUT'])]
    #[Route(path: '/chapters/{chapterId}/promulgators/{nodeId:promulgator}', methods: ['PUT'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Put(
        summary: 'Update Promulgator',
        requestBody: new OA\RequestBody(
            required: true,
            content: new Nelmio\Model(type: UpdatePromulgatorRequest::class)
        ),
        responses: [
            new OA\Response(response: 202, description: 'Update successful', content: new Nelmio\Model(type: PromulgatorDto::class)),
            new OA\Response(response: 400, description: 'Update error', content: new Nelmio\Model(type: ErrorResponseDto::class)),
        ]
    )]
    public function update(
        Project                  $project,
        Promulgator              $promulgator,
        #[MapRequestPayload(acceptFormat: 'json')]
        UpdatePromulgatorRequest $request,
    ): JsonResponse {
        $this->promulgatorService->update($promulgator, $request);
        return $this->json($promulgator, Response::HTTP_OK);
    }

    #[Route(path: '/promulgators/{nodeId:promulgator}', methods: ['DELETE'])]
    #[Route(path: '/chapters/{chapterId}/promulgators/{nodeId:promulgator}', methods: ['DELETE'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Delete(
        summary: 'Delete Promulgator',
        responses: [
            new OA\Response(response: 204, description: 'Deleted successfully'),
            new OA\Response(response: 400, description: 'Delete error', content: new Nelmio\Model(type: ErrorResponseDto::class)),
        ]
    )]
    public function delete(Project $project, Promulgator $promulgator): JsonResponse
    {
        $this->promulgatorService->delete($promulgator);
        return new JsonResponse(null, Response::HTTP_OK);
    }

    #[Route(path: '/promulgators/{nodeId:promulgator}/move', methods: ['PUT'])]
    #[Route(path: '/chapters/{chapterId}/promulgators/{nodeId:promulgator}/move', methods: ['PUT'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Put(
        summary: 'Move Promulgator',
        requestBody: new OA\RequestBody(required: true, content: new Nelmio\Model(type: MovePromulgatorRequest::class)),
        responses: [
            new OA\Response(response: 200, description: 'Move is valid', content: new Nelmio\Model(type: MovePromulgatorResponse::class)),
            new OA\Response(response: 202, description: 'Moved successfully', content: new Nelmio\Model(type: MovePromulgatorResponse::class)),
            new OA\Response(response: 400, description: 'Move error', content: new Nelmio\Model(type: ErrorResponseDto::class)),
        ]
    )]
    public function move(
        Project                $project,
        Promulgator            $promulgator,
        #[MapRequestPayload(acceptFormat: 'json')]
        MovePromulgatorRequest $request,
    ): JsonResponse {
        $response = $this->promulgatorService->move($project, $promulgator, $request);
        return $this->json($response, Response::HTTP_OK);
    }

    #[Route(path: '/promulgators/{nodeId:promulgator}/move-standard', methods: ['POST'])]
    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Post(
        summary: 'Add Reference Standard to Promulgator',
        requestBody: new OA\RequestBody(
            required: true,
            content: new Nelmio\Model(type: AddStandardToPromulgatorRequest::class)
        ),
        responses: [
            new OA\Response(response: 201, description: 'Created', content: new Nelmio\Model(type: AddStandardToPromulgatorResponse::class)),
            new OA\Response(response: 400, description: 'Bad Request', content: new Nelmio\Model(type: ErrorResponseDto::class)),
        ]
    )]
    public function addStandard(
        Project                         $project,
        Promulgator                     $promulgator,
        #[MapRequestPayload(acceptFormat: 'json')]
        AddStandardToPromulgatorRequest $request,
    ): JsonResponse {
        $response = $this->promulgatorService->addStandard($promulgator, $request);
        return $this->json($response, Response::HTTP_CREATED);
    }
}
