<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller;

use App\Entity\File\RemoteFile;
use App\Entity\NoteAttachment;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

use function mb_strlen;
use function sprintf;

class ImageController
{
    #[Route(path: '/images/{hash:file<[a-z0-9]{32,64}>}', name: 'app_remote_image', methods: ['GET'])]
    #[Route(path: '/images/{hash:file<[a-z0-9]{32,64}>}.{_format}', name: 'app_remote_image_with_ext', methods: ['GET'])]
    public function image(RemoteFile $file, string $_format = ''): Response
    {
        return new Response($file->getBinaryContents(), 200, [
            'content-type' => $file->getMimeType(),
        ]);
    }

    #[Route(path: '/attachment/{hash:file<[a-z0-9]{32,64}>}', name: 'app_note_attachment', methods: ['GET'])]
    public function attachment(RemoteFile $file, EntityManagerInterface $em): Response
    {
        /** @var NoteAttachment|null $attachment */
        $attachment = $em->getRepository(NoteAttachment::class)->findOneBy(['file' => $file]);
        $filename = $attachment
            ? $attachment->getFilename()
            : $file->getHash();

        return new Response($file->getBinaryContents(), 200, [
            'content-type'        => $file->getMimeType(),
            'content-disposition' => sprintf('attachment; filename="%s"', $filename),
            'Content-Length'      => (string) mb_strlen($file->getBinaryContents()),
        ]);
    }
}
