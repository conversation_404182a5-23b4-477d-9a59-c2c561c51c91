<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Entity\User;

use App\Entity\Project;
use App\Entity\ProjectCategory;
use App\Security\Model\UserInterface;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Lexik\Bundle\JWTAuthenticationBundle\Security\User\JWTUserInterface;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Serializer\Annotation\Groups;

use function array_fill;
use function array_merge;
use function array_unique;
use function count;
use function in_array;
use function mb_strtoupper;
use function serialize;
use function sprintf;
use function trim;
use function unserialize;

#[ORM\Entity]
#[ORM\Table(name: 'cdp_user')]
class User implements UserInterface, JWTUserInterface, PasswordAuthenticatedUserInterface
{
    #[ORM\Id]
    #[ORM\Column(type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[Groups(['getUser', 'user:read', 'getCategory', 'category:read', 'getProject', 'project:read', 'cdp'])]
    protected ?int $id = null;

    #[ORM\Column(type: 'string', length: 36, unique: true, nullable: true)]
    protected ?string $uuid = null;

    #[ORM\Column(name: 'email', type: 'string', length: 255)]
    #[Groups(['getUser', 'user:read', 'getCategory', 'category:read', 'getProject', 'project:read', 'cdp'])]
    protected string $email = '';

    #[ORM\Column(name: 'first_name', type: 'string', length: 255)]
    #[Groups(['getUser', 'user:read', 'getCategory', 'category:read', 'getProject', 'project:read', 'cdp'])]
    protected string $firstName = '';

    #[ORM\Column(name: 'last_name', type: 'string', length: 255)]
    #[Groups(['getUser', 'user:read', 'getCategory', 'category:read', 'getProject', 'project:read', 'cdp'])]
    protected string $lastName = '';

    #[ORM\Column(name: 'suffix', type: 'string', nullable: true)]
    #[Groups(['getUser', 'user:read', 'putUser'])]
    protected ?string $suffix = '';

    #[ORM\Column(name: 'last_login', type: 'date')]
    #[Groups(['getUser', 'user:read', 'putUser'])]
    protected DateTime $lastLogin;

    #[ORM\Column(name: 'deleted_at', type: 'datetime', nullable: true)]
    #[Groups(['getUser', 'user:read', 'putUser'])]
    protected ?DateTime $deletedAt = null;

    #[ORM\Column(name: 'roles', type: 'array')]
    #[Groups(['getUser', 'user:read', 'putUser', 'getProject', 'project:read'])]
    protected array $roles = [];

    #[ORM\Column(name: 'overridden_roles', type: 'array')]
    #[Groups(['getUser', 'user:read', 'putUser'])]
    protected array $overriddenRoles = [];

    /**
     * @var Collection<int, ProjectCategory>
     *
     */
    #[ORM\ManyToMany(targetEntity: ProjectCategory::class, inversedBy: 'users', cascade: ['all'])]
    #[ORM\JoinTable(name: 'user_categories')]
    #[Groups(['getUser', 'user:read', 'putUser'])]
    private Collection $categories;

    /**
     * @var Collection<int, Project>
     *
     */
    #[ORM\ManyToMany(targetEntity: Project::class, inversedBy: 'users')]
    #[ORM\JoinTable(name: 'user_projects')]
    #[Groups(['getUser', 'user:read', 'putUser'])]
    private Collection $projects;

    public function __construct()
    {
        $this->categories = new ArrayCollection();
        $this->projects = new ArrayCollection();
        $this->lastLogin = new DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUuid(): ?string
    {
        return $this->uuid;
    }

    public function setUuid(?string $uuid): self
    {
        $this->uuid = $uuid;
        return $this;
    }

    public function getLastLogin(): DateTime
    {
        return $this->lastLogin;
    }

    public function setLastLogin(?DateTime $time): self
    {
        $this->lastLogin = $time;
        return $this;
    }

    #[ORM\PrePersist]
    public function setCreatedAtValue(): void
    {
        $this->setLastLogin(new DateTime());
    }

    #[ORM\PreUpdate]
    public function setPreUpdateValues(): void
    {
        $this->setLastLogin(new DateTime());
    }

    public function getUsername(): string
    {
        return $this->email;
    }

    public function getUserIdentifier(): string
    {
        return $this->email;
    }

    public function getName(): string
    {
        return trim(sprintf('%s %s', $this->firstName, $this->lastName));
    }

    public function getFirstName(): string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;
        return $this;
    }

    public function getLastName(): string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;
        return $this;
    }

    public function getSuffix(): string
    {
        return $this->suffix;
    }

    public function setSuffix(string $suffix): self
    {
        $this->suffix = $suffix;
        return $this;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;
        return $this;
    }

    public function addCategory(ProjectCategory $category): void
    {
        if (!$this->categories->contains($category)) {
            $this->categories->add($category);
        }
    }

    /**
     * @return ProjectCategory[]
     */
    public function getCategories(): iterable
    {
        return $this->categories;
    }

    /**
     * @param ProjectCategory[] $categories
     */
    public function setCategories(array $categories): self
    {
        $this->categories = new ArrayCollection($categories);
        return $this;
    }

    /**
     * @return Project[]
     */
    public function getProjects(): iterable
    {
        return $this->projects;
    }

    /**
     * @param Project[] $projects
     */
    public function setProjects(array $projects): self
    {
        $this->projects = new ArrayCollection($projects);
        return $this;
    }

    public function getDeletedAt(): ?DateTime
    {
        return $this->deletedAt;
    }

    public function setDeletedAt(?DateTime $deletedAt = null): self
    {
        $this->deletedAt = $deletedAt;
        return $this;
    }

    public function getOverriddenRoles(): array
    {
        return $this->overriddenRoles;
    }

    public function setOverriddenRoles(array $overriddenRoles): self
    {
        $this->overriddenRoles = $overriddenRoles;
        return $this;
    }

    public function getRoles(): array
    {
        $roles = $this->roles;
        $roles[] = static::ROLE_DEFAULT;

        return array_unique($roles);
    }

    public function setRoles(array $roles): self
    {
        $this->roles = [];

        foreach ($roles as $role) {
            $this->addRole($role);
        }

        return $this;
    }

    public function addRole(string $role): self
    {
        $role = mb_strtoupper($role);
        if ($role === static::ROLE_DEFAULT) {
            return $this;
        }

        if (!in_array($role, $this->getRoles(), true)) {
            $this->roles[] = $role;
        }

        return $this;
    }

    public function hasRole(string $role): bool
    {
        $roles = count($this->getOverriddenRoles()) > 0
            ? $this->getOverriddenRoles()
            : $this->getRoles();

        return in_array(mb_strtoupper($role), $roles, true);
    }

    public function getSalt(): ?string
    {
        return null;
    }

    #[\Deprecated]
    public function eraseCredentials(): void
    {
    }

    public function getPassword(): ?string
    {
        return null;
    }

    public function __toString(): string
    {
        return $this->getName();
    }

    public function __serialize(): array
    {
        return [
            $this->email,
            $this->id,
        ];
    }

    public function __unserialize(array $data): void
    {
        [
            $this->email,
            $this->id,
        ] = $data;
    }

    /** @param string $username */
    public static function createFromPayload($username, array $payload): JWTUserInterface
    {
        $user = new self();
        $user->setEmail($username);

        // Handle missing 'roles' key in JWT payload
        $roles = $payload['roles'] ?? [self::ROLE_DEFAULT];
        $user->setRoles((array) $roles);

        return $user;
    }
}
