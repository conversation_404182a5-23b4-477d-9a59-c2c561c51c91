<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\ObjectMapper;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Appendix;
use App\Entity\CodeBook\BackMatter;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\CopyrightPage;
use App\Entity\CodeBook\Definition;
use App\Entity\CodeBook\DefinitionList;
use App\Entity\CodeBook\Equation;
use App\Entity\CodeBook\Figure;
use App\Entity\CodeBook\Foreword;
use App\Entity\CodeBook\FrontMatter;
use App\Entity\CodeBook\Index;
use App\Entity\CodeBook\IndexDivision;
use App\Entity\CodeBook\IndexEntry;
use App\Entity\CodeBook\Preface;
use App\Entity\CodeBook\Promulgator;
use App\Entity\CodeBook\Publication;
use App\Entity\CodeBook\PublisherNote;
use App\Entity\CodeBook\ReferenceStandard;
use App\Entity\CodeBook\RelocatedFrom;
use App\Entity\CodeBook\RelocatedTo;
use App\Entity\CodeBook\SecondaryIndexEntry;
use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Table;
use App\Entity\CodeBook\TertiaryIndexEntry;
use App\Entity\CodeBook\TitlePage;
use App\Entity\CodeBook\Volume;
use App\Exception\ApiException;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlAppendixMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlBackMatterMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlChapterMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlCopyrightPageMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlDefinitionListMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlDefinitionMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlEquationMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlFigureMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlForewordMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlFrontMatterMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlIndexDivisionMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlIndexEntryMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlIndexMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlPrefaceMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlPromulgatorMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlPublicationMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlPublisherNoteMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlReferenceStandardMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlRelocatedFromMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlRelocatedToMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlSecondaryIndexEntryMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlSectionMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlTableMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlTertiaryIndexEntryMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlTitlePageMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlVolumeMapper;
use App\Serializer\Encoder\Xml2\Element\AbstractElement;
use Doctrine\Persistence\Proxy;

use function get_class;
use function get_parent_class;
use function sprintf;

class CodeBookToXmlMapper
{
    public function __construct(
        private readonly CodeBookToXmlAppendixMapper            $appendixMapper,
        private readonly CodeBookToXmlBackMatterMapper          $backMatterMapper,
        private readonly CodeBookToXmlChapterMapper             $chapterMapper,
        private readonly CodeBookToXmlCopyrightPageMapper       $copyrightPageMapper,
        private readonly CodeBookToXmlDefinitionMapper          $definitionMapper,
        private readonly CodeBookToXmlDefinitionListMapper      $definitionListMapper,
        private readonly CodeBookToXmlEquationMapper            $equationMapper,
        private readonly CodeBookToXmlFigureMapper              $figureMapper,
        private readonly CodeBookToXmlForewordMapper            $forewordMapper,
        private readonly CodeBookToXmlFrontMatterMapper         $frontMatterMapper,
        private readonly CodeBookToXmlIndexMapper               $indexMapper,
        private readonly CodeBookToXmlIndexDivisionMapper       $indexDivisionMapper,
        private readonly CodeBookToXmlIndexEntryMapper          $indexEntryMapper,
        private readonly CodeBookToXmlPrefaceMapper             $prefaceMapper,
        private readonly CodeBookToXmlPromulgatorMapper         $promulgatorMapper,
        private readonly CodeBookToXmlPublicationMapper         $publicationMapper,
        private readonly CodeBookToXmlPublisherNoteMapper       $publisherNoteMapper,
        private readonly CodeBookToXmlReferenceStandardMapper   $referenceStandardMapper,
        private readonly CodeBookToXmlRelocatedFromMapper       $relocatedFromMapper,
        private readonly CodeBookToXmlRelocatedToMapper         $relocatedToMapper,
        private readonly CodeBookToXmlSecondaryIndexEntryMapper $secondaryIndexEntryMapper,
        private readonly CodeBookToXmlSectionMapper             $sectionMapper,
        private readonly CodeBookToXmlTableMapper               $tableMapper,
        private readonly CodeBookToXmlTertiaryIndexEntryMapper  $tertiaryIndexEntryMapper,
        private readonly CodeBookToXmlTitlePageMapper           $titlePageMapper,
        private readonly CodeBookToXmlVolumeMapper              $volumeMapper,
    ) {
    }

    /** @throws ApiException */
    public function map(AbstractCodeBookNode $from): AbstractElement
    {
        if ($from instanceof Proxy && !$from->__isInitialized()) {
            $from->__load();
        }

        $class = $from instanceof Proxy
            ? (get_parent_class($from) ?: get_class($from))
            : get_class($from);

        $node = match ($class) {
            Appendix::class            => $this->appendixMapper->map($from),
            BackMatter::class          => $this->backMatterMapper->map($from),
            Chapter::class             => $this->chapterMapper->map($from),
            CopyrightPage::class       => $this->copyrightPageMapper->map($from),
            Definition::class          => $this->definitionMapper->map($from),
            DefinitionList::class      => $this->definitionListMapper->map($from),
            Equation::class            => $this->equationMapper->map($from),
            Figure::class              => $this->figureMapper->map($from),
            Foreword::class            => $this->forewordMapper->map($from),
            FrontMatter::class         => $this->frontMatterMapper->map($from),
            Index::class               => $this->indexMapper->map($from),
            IndexDivision::class       => $this->indexDivisionMapper->map($from),
            IndexEntry::class          => $this->indexEntryMapper->map($from),
            Preface::class             => $this->prefaceMapper->map($from),
            Promulgator::class         => $this->promulgatorMapper->map($from),
            Publication::class         => $this->publicationMapper->map($from),
            PublisherNote::class       => $this->publisherNoteMapper->map($from),
            ReferenceStandard::class   => $this->referenceStandardMapper->map($from),
            RelocatedFrom::class       => $this->relocatedFromMapper->map($from),
            RelocatedTo::class         => $this->relocatedToMapper->map($from),
            SecondaryIndexEntry::class => $this->secondaryIndexEntryMapper->map($from),
            Section::class             => $this->sectionMapper->map($from),
            Table::class               => $this->tableMapper->map($from),
            TertiaryIndexEntry::class  => $this->tertiaryIndexEntryMapper->map($from),
            TitlePage::class           => $this->titlePageMapper->map($from),
            Volume::class              => $this->volumeMapper->map($from),
            default                    => null,
        };

        if (null === $node) {
            throw new ApiException(sprintf('%s is not a supported format', get_class($from)));
        }

        return $node;
    }
}
