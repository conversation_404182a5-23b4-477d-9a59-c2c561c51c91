<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Service;

use App\Dto\Xml2\Cdp\CdpGetLastSync;
use App\Entity\JobStatus;
use Doctrine\ORM\EntityManagerInterface;

class JobStatusService
{
    const string BOOK_PDF_EXPORT_TYPE = 'pdf_export';
    const string BOOK_XML_EXPORT_TYPE = 'book_xml_export';

    public function __construct(private readonly EntityManagerInterface $manager)
    {}

    public function createExportStatus(string $bookId, string $jobType, bool $isRedline = false): JobStatus
    {
        if ($oldJob = $this->getExportStatus($bookId, $jobType)) {
            $this->manager->remove($oldJob);
        }

        $job = new JobStatus();
        $job->setProcessId($this->getProcessId($bookId, $jobType));
        $job->setStatus(JobStatus::STATUS_RUNNING);
        $job->setErrorMessage('');
        $job->setRedline($isRedline);
        $job->setCreatedDate(new \DateTime());

        $this->manager->persist($job);
        $this->manager->flush();

        return $job;
    }

    public function updateExportStatus(JobStatus $jobStatus, string $status, string $errorMessage = ''): void
    {
        $jobStatus->setStatus($status);
        $jobStatus->setErrorMessage($errorMessage);

        $this->manager->persist($jobStatus);
        $this->manager->flush();
    }

    public function getExportStatus(string $bookId, string $jobType): ?object
    {
        $id = $this->getProcessId($bookId, $jobType);

        $job = $this->manager
            ->getRepository(JobStatus::class)
            ->findBy(['processId' => $id], ['id' => 'DESC'], 1, 0);

        return $job ? $job[0] : null;
    }

    public function getProcessId(string $bookId, string $jobType): string
    {
        return "{$jobType}_{$bookId}";
    }

    public function getDetailedJobStatus(string $bookId, string $jobType): ?CdpGetLastSync
    {
        $jobStatus = $this->getExportStatus($bookId, $jobType);

        if (!$jobStatus) {
            return null;
        }

        return new CdpGetLastSync(
            'Unknown',
            $jobStatus->getStatus(),
            $jobStatus->getCreatedDate()
        );
    }
}
