<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\ErrorResponseDto;
use App\Dto\ResponseDtoInterface;
use App\Dto\Xml2\Relocation\GetRelocationOptionsRequest;
use App\Dto\Xml2\Relocation\GetRelocationOptionsResponse;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Appendix;
use App\Entity\CodeBook\BackMatter;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\Definition;
use App\Entity\CodeBook\DefinitionList;
use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Volume;
use App\Entity\Interfaces\RelocatableNodeInterface;
use App\Entity\Project;
use App\Enum\ObjectType;
use App\Repository\CodeBookNodeRepository;
use App\Service\CodeBook\CodeBookPathIdService;
use App\Traits\NumberCleanerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;

class RelocationService extends AbstractNodeService
{
    use NumberCleanerTrait;

    public const INSERT_BEFORE = 'before';
    public const INSERT_AFTER  = 'after';
    public const INSERT_LAST   = 'insert last';
    public const INSERT_FIRST  = 'insert first';

    public function __construct(
        EntityManagerInterface                 $em,
        CodeBookNodeRepository                 $repo,
        Security                               $security,
        private readonly CodeBookPathIdService $nodePathService,
        private readonly AppendixService       $appendixService,
        private readonly PromulgatorService    $promulgatorService,
        private readonly SectionService        $sectionService
    ) {
        parent::__construct($em, $repo, $security);
    }

    public function getRelocationOptions(Project $project, GetRelocationOptionsRequest $request): ResponseDtoInterface
    {
        switch ($request->type) {
            case ObjectType::TYPE_SECTION:
            case ObjectType::TYPE_CODE_SECTION:
                return $this->handleSectionRelocation($project, $request);

            case ObjectType::TYPE_CHAPTER:
                return $this->handleChapterRelocation($project, $request);

            case ObjectType::TYPE_APPENDIX:
                return $this->handleAppendixRelocation($project, $request);

            case ObjectType::TYPE_FIGURE:
            case ObjectType::TYPE_TABLE:
                return $this->handleTableFigureRelocation($request);

            case ObjectType::TYPE_PROMULGATOR:
                return $this->handlePromulgatorRelocation($request, $project->getCodeBook()->getVolume());

            case ObjectType::TYPE_DEFINITION:
                return $this->handleDefinitionRelocation($request);

            //TODO: Add more cases for other types in the future, e.g.:
            // case ObjectType::TYPE_CHAPTER:
            //     return $this->handleChapterRelocation($request);

            default:
                return new ErrorResponseDto('Unsupported relocation type.');
        }
    }

    public function getWrapOptions(Project $project, GetRelocationOptionsRequest $request): ResponseDtoInterface
    {
        switch ($request->type) {
            case ObjectType::TYPE_SECTION:
            case ObjectType::TYPE_CODE_SECTION:
                return $this->handleSectionWrap($request);

            default:
                return new ErrorResponseDto('Unsupported relocation type.');
        }
    }

    private function handleTableFigureRelocation(GetRelocationOptionsRequest $request): ResponseDtoInterface
    {
        $sectionParent = $this->repo->findNodeById($request->parentId);

        if (null === $sectionParent) {
            return $this->createErrorResponse(sprintf('Parent "%s" not found.', $request->parentId));
        }

        $firstOrdinalChar = substr(ltrim($request->number), 0, 1);
        // Check if ordinal does not start with one of character from array
        if (in_array($firstOrdinalChar, ['[', '('])) {
            return $this->createErrorResponse(
                sprintf('Labels are not allowed in the ordinal field: %s. Add label to label field.', $request->number)
            );
        }

        return $this->createSuccessResponse(
            $sectionParent->getNodeId(),
            '',
            self::INSERT_AFTER,
            $request->number
        );
    }

    private function handlePromulgatorRelocation(
        GetRelocationOptionsRequest $request,
        Volume                      $volume
    ): ResponseDtoInterface {
        $parentNode = $this->repo->findNodeById($request->parentId);

        if (null === $parentNode) {
            return $this->createErrorResponse(sprintf('Parent "%s" not found.', $request->parentId));
        }

        if (!$this->isPromulgatorParentChapter($volume, $parentNode)) {
            return $this->createErrorResponse(
                sprintf('Parent "%s" is not a valid promulgator parent chapter.', $request->parentId)
            );
        }

        if (!$this->isValidAcronym($request->number)) {
            return $this->createErrorResponse(
                sprintf('Invalid or empty acronym provided : "%s".', $request->number)
            );
        }

        return $this->createSuccessResponse($parentNode->getNodeId());
    }

    private function handleSectionRelocation(
        Project                     $project,
        GetRelocationOptionsRequest $request
    ): ResponseDtoInterface {
        $parentNode = $this->repo->findNodeById($request->parentId);

        if (null === $parentNode) {
            return $this->createErrorResponse(sprintf('Parent "%s" not found.', $request->parentId));
        }

        $cleanNewOrdinal = $this->normalizeNumber($this->getCleanOrdinal($request->number));
        foreach ($parentNode->getChildren(false) as $sibling) {
            if (!$sibling instanceof RelocatableNodeInterface) {
                continue;
            }
            if ($sibling->getNodeId() === $request->relocatingId) {
                continue;
            }

            $siblingOrdinal = $this->normalizeNumber($this->getCleanOrdinal($sibling->getNumber()));
            if ($siblingOrdinal === $cleanNewOrdinal) {
                return $this->createErrorResponse(sprintf('Section "%s" already exists.', $request->number));
            }
        }

        $location = $this->findSectionNeighborByNumber($parentNode, $request->number, $request->relocatingId);

        return $this->createSuccessResponse(
            $parentNode->getNodeId(),
            $location['neighbor'],
            $location['action'],
            $request->number
        );
    }

    private function handleDefinitionRelocation(GetRelocationOptionsRequest $request): ResponseDtoInterface
    {
        $parentNode = $this->repo->findNodeById($request->parentId);

        if (null === $parentNode) {
            return $this->createErrorResponse(sprintf('Parent "%s" not found.', $request->parentId));
        }

        $defList = $parentNode->firstDefinitionList($parentNode);
        if ($defList) {
            foreach ($defList->getChildren() as $terms) {
                if (method_exists($terms, 'getTerm') && (strtolower($this->getCleanOrdinal($terms->getTerm())) == strtolower($request->number))) {
                    return $this->createErrorResponse(sprintf('Definition "%s" already exists.', $request->number));
                }
            }
        } else {
            $defList = new DefinitionList();
            $defList->setParent($parentNode);
            $defList->setPosition(count($parentNode->getChildren(false)));
        }

        $newDefinition = new Definition();
        $newDefinition->setParent($defList);
        $newDefinition->setTerm($request->number);


        return $this->createSuccessResponse(
            $defList->getNodeId(),
            $parentNode->getNodeId(),
            '',
            $request->number
        );
    }

    private function handleChapterRelocation(
        Project                     $project,
        GetRelocationOptionsRequest $request
    ): ResponseDtoInterface {
        if (!ctype_digit($request->number)) {
            return $this->createErrorResponse(
                sprintf('The number "%s" must be an integer when type is "chapter".', $request->number)
            );
        }

        $parentNode = $project->getCodeBook()->getVolumeByNumber($request->parentId) ?? $project->getCodeBook()->getVolume();

        if (null === $parentNode) {
            return $this->createErrorResponse(sprintf('Parent "%s" not found.', $project->getShortCode()));
        }

        if ($this->isSamePosition($project, $request->number, $request->relocatingId)) {
            return $this->createErrorResponse(
                sprintf('Chapter "%s" is already at the same position.', $request->number)
            );
        }
        if ($this->doesChapterExists($project, $request->number, $request->relocatingId)) {
            return $this->createErrorResponse(
                sprintf('Chapter "%s" already exists.', $request->number)
            );
        }

        $location = $this->findSectionNeighborByNumber($parentNode, $request->number, $request->relocatingId);

        return $this->createSuccessResponse(
            $parentNode->getNodeId(),
            $location['neighbor'],
            $location['action'],
            $request->number
        );
    }

    private function handleAppendixRelocation(
        Project                     $project,
        GetRelocationOptionsRequest $request
    ): ResponseDtoInterface {
        $newAppendix = $this->getCleanOrdinal($request->number);
        if ($this->appendixService->checkAppendixNumber($request->number)) {
            return $this->createErrorResponse(
                sprintf('The provided appendix number "%s" is invalid.', $request->number)
            );
        }
        $parentNode = ($project->getCodeBook()->getVolumeByNumber($request->parentId)
            ?? $project->getCodeBook()->getVolume())
            ->getBackMatter();


        if (null === $parentNode) {
            return $this->createErrorResponse(sprintf('Parent "%s" not found.', $project->getShortCode()));
        }

        foreach ($parentNode->getChildren(false) as $child) {
            if ($child instanceof Appendix && $this->getCleanOrdinal($child->getNumber()) == $newAppendix) {
                return $this->createErrorResponse(
                    sprintf('The appendix "%s" already exists.', $request->number)
                );
            }
        }

        $location = $this->findAppendixNeighborByNumber($parentNode, $request->number, $request->relocatingId);

        return $this->createSuccessResponse(
            $parentNode->getNodeId(),
            $location['neighbor'],
            $location['action'],
            $request->number
        );
    }

    private function createErrorResponse(string $message): GetRelocationOptionsResponse
    {
        $response = new GetRelocationOptionsResponse();
        $response->isValidRelocation = false;
        $response->message = $message;
        return $response;
    }

    private function createSuccessResponse(
        string $parentId,
               $neighbor = null,
               $action = null,
               $newNumber = null
    ): GetRelocationOptionsResponse {
        $response = new GetRelocationOptionsResponse();
        $response->isValidRelocation = true;
        $response->parentId = $parentId;
        $response->newNumber = $newNumber;
        $response->defaultNeighbor = $neighbor;
        $response->action = $action;
        return $response;
    }

    public function isValidAcronym(string $acronym): bool
    {
        $parsedAcronym = $this->promulgatorService->parseAcronym($acronym);

        if (strlen($parsedAcronym) === 0) {
            return false;
        }

        if (strlen($this->promulgatorService->sanitizeAcronym($parsedAcronym)) === 0) {
            return false;
        }

        return true;
    }

    private function findSectionNeighborByNumber(
        AbstractCodeBookNode $parentNode,
        string               $newNumber,
        string               $relocatingId
    ): array {
        $siblings = array_filter($parentNode->getChildren(false), function ($sibling) use ($relocatingId) {
            return $sibling->getNodeId() !== $relocatingId;
        });
        $action = null;
        $neighbor = null;

        foreach ($siblings as $sibling) {
            if ($sibling instanceof RelocatableNodeInterface) {

                $siblingNumberRaw = $this->getCleanOrdinal($sibling->getNumber());
                $cleanSibling = $this->normalizeNumber($siblingNumberRaw);
                $cleanNew = $this->normalizeNumber($newNumber);

                if (version_compare($cleanNew, $cleanSibling, '<')) {
                    $action = self::INSERT_BEFORE;
                    $neighbor = $sibling;
                    break;
                }
                $neighbor = $sibling;
                $action = self::INSERT_AFTER;
            }
        }

        if ($action === null) {
            $action = self::INSERT_LAST;
            $neighbor = $parentNode;
        }

        return ['neighbor' => $neighbor->getNodeId(), 'neighborNumber' => $neighbor->getNumber(), 'action' => $action];
    }

    private function findAppendixNeighborByNumber(
        BackMatter $parentNode,
        string     $newAppendixNumber,
        string     $relocatingId
    ): array {
        $siblings = array_filter($parentNode->getAllAppendices(), function ($child) use ($relocatingId) {
            return $child->getNodeId() !== $relocatingId;
        });
        $action = null;
        $neighbor = null;

        foreach ($siblings as $sibling) {
            $siblingNumber = $this->getCleanOrdinal($sibling->getNumber());

            if ($this->compareAppendixNumbers($newAppendixNumber, $siblingNumber) < 0) {
                $action = self::INSERT_BEFORE;
                $neighbor = $sibling;
                break;
            }

            $neighbor = $sibling;
            $action = self::INSERT_AFTER;
        }

        if ($action === null) {
            $action = self::INSERT_LAST;
            $neighbor = $parentNode;
        }

        return ['neighbor' => $neighbor->getNodeId(), 'action' => $action];
    }

    private function compareAppendixNumbers(string $a, string $b): int
    {
        if (ctype_alpha($a) && ctype_alpha($b)) {
            return strcmp($a, $b);
        }

        return version_compare($a, $b);
    }

    private function handleSectionWrap(GetRelocationOptionsRequest $request): ResponseDtoInterface
    {

        $relocatingSection = $this->repo->findNodeById($request->relocatingId);
        if (!$relocatingSection instanceof Section) {
            return $this->createErrorResponse(
                sprintf('Relocating Section "%s" not found.', $request->relocatingId)
            );
        }

        $old = $this->normalizeNumber(
            $this->getCleanOrdinal($relocatingSection->getNumber())
        );
        $new = $this->normalizeNumber($request->number);

        if (!preg_match('/^' . preg_quote($old, '/') . '\.\d+$/', $new)) {
            return $this->createErrorResponse(sprintf(
                'Invalid number "%s". For a wrap, the new number must be an immediate child of "%s" (e.g. "%s.1").',
                $request->number,
                $old,
                $old
            ));
        }

        $location = $this->findSectionNeighborByNumber($relocatingSection, $request->number, $request->relocatingId);

        return $this->createSuccessResponse(
            $relocatingSection->getParent()->getNodeId(),
            $location['neighbor'],
            $location['action'],
            $request->number
        );
    }

    private function isPromulgatorParentChapter(Volume $volume, AbstractCodeBookNode $candidate): bool
    {
        foreach ($volume->getPromulgatorParentChapters() as $node) {
            if ($node->getNodeId() === $candidate->getNodeId()) {
                return true;
            }
        }
        return false;
    }

    /**
     * Validates chapter relocation by checking for conflicts and same position scenarios.
     *
     * @param Project $project
     * @param string $number Target chapter number
     * @param string $relocatingId ID of the chapter being relocated
     * @return array Returns ['isValid' => bool, 'reason' => string|null, 'isSamePosition' => bool]
     */
    private function validateChapterRelocation(Project $project, string $number, string $relocatingId): array
    {
        $codeBook = $project->getCodeBook();
        $currentChapterFound = false;

        foreach ($codeBook->getVolumes() as $volume) {
            foreach ($volume->getChildren(false) as $child) {
                if ($child instanceof Chapter) {
                    $cleanChildNumber = $this->getCleanOrdinal($child->getNumber());

                    if ($child->getNodeId() === $relocatingId) {
                        // Found the chapter being relocated
                        $currentChapterFound = true;
                        if ($cleanChildNumber === $number) {
                            return [
                                'isValid' => false,
                                'reason' => 'same_position',
                                'isSamePosition' => true
                            ];
                        }
                    } elseif ($cleanChildNumber === $number) {
                        // Found another chapter with the target number
                        return [
                            'isValid' => false,
                            'reason' => 'chapter_exists',
                            'isSamePosition' => false
                        ];
                    }
                }
            }
        }

        return [
            'isValid' => $currentChapterFound,
            'reason' => $currentChapterFound ? null : 'chapter_not_found',
            'isSamePosition' => false
        ];
    }
}
