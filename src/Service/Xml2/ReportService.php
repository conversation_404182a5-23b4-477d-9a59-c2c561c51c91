<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\Xml2\Reports\Chapter\GetChapterPdfRequest;
use App\Dto\Xml2\Reports\Chapter\GetChapterXmlExportRequest;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Appendix;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\Definition;
use App\Entity\CodeBook\Figure;
use App\Entity\CodeBook\Promulgator;
use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Table;
use App\Entity\JobStatus;
use App\Entity\Project;
use App\ObjectMapper\CodeBookToXmlMapper;
use App\Repository\AuditLogRepository;
use App\Repository\CodeBookNodeRepository;
use App\Serializer\Encoder\Xml2\Xml2Encoder;
use App\Service\Builder\IsWasReport\ReportFieldExtractorFactory;
use App\Service\JobStatusService;
use App\Traits\AttachmentResponseTrait;
use App\Traits\CsvExportTrait;
use App\Traits\NotesIncludedTrait;
use App\Traits\ReportsName;
use Doctrine\ORM\EntityManagerInterface;
use ICC\Component\CHub\CHubService;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Serializer\SerializerInterface;

use function array_map;
use function file_exists;
use function json_decode;
use function preg_replace;
use function sprintf;
use function unlink;

class ReportService extends AbstractNodeService
{
    use ReportsName;
    use AttachmentResponseTrait;
    use CsvExportTrait;
    use NotesIncludedTrait;

    const STATUS_IDLE        = 'IDLE';
    const STATUS_IN_PROGRESS = 'IN_PROGRESS';
    const STATUS_FAILED      = 'FAILED';

    public function __construct(
        EntityManagerInterface                $em,
        CodeBookNodeRepository                $repo,
        Security                              $security,
        private readonly SerializerInterface  $serializer,
        private readonly CHubService          $cHubService,
        private readonly SectionService       $sectionService,
        private readonly AuditLogRepository   $auditLogRepository,
        private readonly JobStatusService     $jobStatusService,
        private readonly XmlValidationService $xmlValidationService,
        private readonly string               $kernelRootDir,
        private readonly string               $apiUrl,
        private readonly CodeBookToXmlMapper  $codeBookToXmlMapper,
        private readonly Xml2Encoder          $xml2Encoder,
    ) {
        parent::__construct($em, $repo, $security);
    }

    public function externalReferenceLinksReportCSV(Project $project): Response
    {
        $externalLinks = [];

        $rows = array_map(function ($link) {
            $node = $this->em->getRepository(AbstractCodeBookNode::class)
                             ->findOneBy(['nodeId' => $link->getParentId()]);
            return [
                'location' => strip_tags($node->getNumber() . ' ' . $node->getTitle()),
                'href'     => $link->getReferenceId(),
                'text'     => $link->getLinkText(),
            ];
        }, $externalLinks);

        // Add header row
        array_unshift($rows, [
            'location' => 'Location',
            'href'     => 'Href',
            'text'     => 'Link Text',
        ]);

        $csvContent = $this->convertArrayToCsv($rows);
        $filename = sprintf('%s-External-Reference-Links.csv', $project->getShortCode());

        return $this->createAttachmentResponse($filename, $csvContent, 'text/csv');
    }

    public function chapterXmlValidationCsv(Project $project, AbstractCodeBookNode $chapter): Response
    {
        $issues = $this->xmlValidationService->validateChapter($chapter);

        if ($issues === []) {
            return new Response('', Response::HTTP_NO_CONTENT);
        }

        $rows = [
            ['number','sectionId','type', 'title', 'body'],
        ];

        foreach ($issues as $issue) {
            $rows[] = [
                $issue->number ?? '',
                $issue->nodeId ?? '',
                $issue->nodeType ?? '',
                $issue->title ?? '',
                $issue->snippet ?? '',
            ];
        }

        $csvContent = $this->convertArrayToCsv($rows);
        if ($csvContent instanceof Response) {
            return $csvContent;
        }

        $filename = sprintf(
            '%s-%s-xml-validation.csv',
            $project->getShortCode(),
            $chapter->getNodeId()
        );

        return $this->createAttachmentResponse($filename, $csvContent, 'text/csv');
    }

    public function codeChangesReport(AbstractCodeBookNode $node, GetChapterPdfRequest $request): Response
    {
        $reportTitle = $this->getReportFileName($node, $request);

        if ($request->notes !== 'none') {
            if ($node instanceof Appendix || $node instanceof Chapter || $node instanceof Section) {
                $body = $this->appendInternalNotesToBody($node->getBody(), $node, $request->notes);
                $node->setBody($body);
            } elseif ($node instanceof Table) {
                $table = $this->appendInternalNotesToBody($node->getTable(), $node, $request->notes);
                $node->setTable($table);
            } elseif ($node instanceof Figure) {
                $media = $this->appendInternalNotesToBody($node->getMedia(), $node, $request->notes);
                $node->setMedia($media);
            }
        }

        $xml2element = $this->codeBookToXmlMapper->map($node);
        $xml = $this->xml2Encoder->encode($xml2element);

        $filename = sprintf('%s.zip', $reportTitle);
        $zip = new \ZipArchive();
        $zip->open($filename, \ZipArchive::CREATE | \ZipArchive::OVERWRITE);
        $zip->addFromString('file.xml', $xml);
        $zip->close();

        return $this->getPdfFile($filename);
    }

    public function chapterXmlExport(AbstractCodeBookNode $node, GetChapterXmlExportRequest $request): Response
    {
        $reportTitle = $this->getXmlReportName($node, $request);

        if ($request->notes !== 'none') {
            $context = ['notesType' => $request->notes];
            if ($node instanceof Appendix || $node instanceof Chapter || $node instanceof Section) {
                $body = $this->appendInternalNotesToBody($node->getBody(), $node, $request->notes);
                $node->setBody($body);
            } elseif ($node instanceof Table) {
                $table = $this->appendInternalNotesToBody($node->getTable(), $node, $request->notes);
                $node->setTable($table);
            } elseif ($node instanceof Figure) {
                $media = $this->appendInternalNotesToBody($node->getMedia(), $node, $request->notes);
                $node->setMedia($media);
            }
        }

        $xml2element = $this->codeBookToXmlMapper->map($node);
        $xml = $this->xml2Encoder->encode($xml2element);

        $zip = new \ZipArchive();
        $filename = sprintf('%s.zip', $reportTitle);
        $zip->open($filename, \ZipArchive::CREATE | \ZipArchive::OVERWRITE);
        $zip->addFromString(sprintf("%s.xml", $reportTitle), $xml);
        $zip->close();

        $content = file_get_contents($filename);
        $response = new Response($content, 200, ['Content-Type' => 'application/zip']);
        $disposition = $response->headers->makeDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            $filename
        );

        if (file_exists($filename)) {
            unlink($filename);
        }

        $response->headers->set('Content-Disposition', $disposition);

        return $response;
    }

    public function getPdfFile(string $filename): Response
    {
        $content = $this->cHubService->getServiceApi()->convertZipToPdf([
            'media'   => 'print',
            'zipFile' => new UploadedFile(
                $filename,
                $filename,
                'application/zip',
                null,
                true
            ),
        ]);

        if (file_exists($filename)) {
            unlink($filename);
        }

        $filename = preg_replace('/\.zip$/i', '.pdf', $filename);
        $headers = [
            'Content-Type'        => 'application/pdf',
            'Content-Disposition' => 'inline; filename=' . $filename,
        ];

        return new Response($content->getContent(), 200, $headers);
    }

    public function getIsWasReport(
        Project              $project,
        AbstractCodeBookNode $chapter,
        bool                 $trailing
    ): Response {

        $book = $project->getCodeBook()->getVolume();
        $bookId = $book->getNodeId();

        if ($chapter instanceof Definition) {
            $headerField = 'Term';
        } elseif ($chapter instanceof Promulgator) {
            $headerField = 'Acronym';
        } else {
            $headerField = 'Number';
        }

        $csvData = [];
        $csvData[] = [
            $bookId . ' Old ' . $headerField,
            $bookId . ' Old Title',
            $bookId . ' New ' . $headerField,
            $bookId . ' New Title',
            'Comments',
        ];

        $logs = $this->auditLogRepository->findByChapterIdWithJsonKeys($chapter->getNodeId(), ['title', 'number', 'acronym', 'term']);

        foreach ($logs as $log) {
            $changes = $log['changes'];
            $action = $log['action'];

            $modifiedNode = $this->repo->findNodeById($log['node_id']);
            if (!$modifiedNode) {
                continue;
            }

            $extractor = ReportFieldExtractorFactory::create($modifiedNode);

            $oldField = '';
            $oldTitleField = '';

            if ($action === 'created') {
                $newField = $extractor->getNewNumber($modifiedNode, $changes);
                $newTitleField = $extractor->getNewTitle($modifiedNode, $changes);
            } elseif ($action === 'updated') {
                if ((isset($changes['number']) || isset($changes['acronym']) || isset($changes['term'])) && !isset($changes['title'])) {
                    $oldField = $extractor->getOldNumber($modifiedNode, $changes);
                    $newField = $extractor->getNewNumber($modifiedNode, $changes);

                    $oldTitleField = $extractor->getNewTitle($modifiedNode, []);
                    $newTitleField = $extractor->getNewTitle($modifiedNode, []);
                } elseif ((isset($changes['number']) || isset($changes['acronym']) || isset($changes['term'])) && isset($changes['title'])) {
                    $oldField = $extractor->getOldNumber($modifiedNode, $changes);
                    $newField = $extractor->getNewNumber($modifiedNode, $changes);
                    $oldTitleField = $extractor->getOldTitle($modifiedNode, $changes);
                    $newTitleField = $extractor->getNewTitle($modifiedNode, $changes);
                } else {
                    $newField = $extractor->getNewNumber($modifiedNode, []);
                    $newTitleField = $extractor->getNewTitle($modifiedNode, []);
                }
            } else {
                $newField = $extractor->getNewNumber($modifiedNode, []);
                $newTitleField = $extractor->getNewTitle($modifiedNode, []);
            }

            $csvData[] = [
                $oldField,
                $oldTitleField,
                $newField,
                $newTitleField,
                $action,
            ];
        }

        $csvContent = $this->convertArrayToCsv($csvData);
        $filename = $chapter->getNodeId() . ($trailing ? ' Is-Was Report with Trail Data.csv' : ' Is-Was Report.csv');

        return $this->createAttachmentResponse($filename, $csvContent, 'text/csv');
    }

    public function getStatus(
        string $bookId,
        string $jobType,
        string $folderName = '',
        string $redLineFolderName = ''
    ): array {
        $isPdfJob = $jobType === JobStatusService::BOOK_PDF_EXPORT_TYPE;

        $export = function (?string $folder) use ($bookId, $isPdfJob): array {
            return ($folder !== '')
                ? $this->exportInfo($folder, $bookId, 'api/file', $isPdfJob ? 'pdf' : null)
                : ['name' => null, 'link' => null];
        };

        $clean = $export($folderName);
        $redline = $export($redLineFolderName);

        $jobStatus = $this->jobStatusService->getExportStatus($bookId, $jobType);

        $status = match (true) {
            $jobStatus && $jobStatus->getStatus() === JobStatus::STATUS_RUNNING => self::STATUS_IN_PROGRESS,
            $jobStatus && $jobStatus->getStatus() === JobStatus::STATUS_FAILED  => self::STATUS_FAILED,
            default                                                             => self::STATUS_IDLE,
        };

        $type = ($jobStatus && $jobStatus->getRedline()) ? 'Clean' : 'Redline';
        $errorDetails = [
            'message' => null,
            'issues'  => null,
            'nodeId'  => null,
        ];

        if ($status === JobStatus::STATUS_FAILED && $jobStatus) {
            $errorDetails = $this->extractErrorDetails($jobStatus->getErrorMessage());
        }

        return [
            'status'                        => $status,
            'type'                          => $type,
            'previousExportFileName'        => $clean['name'],
            'previousExportLink'            => $clean['link'],
            'previousRedlineExportFileName' => $redline['name'],
            'previousRedlineExportLink'     => $redline['link'],
            'errorMessage'                  => $errorDetails['message'],
            'xmlValidationIssues'           => $errorDetails['issues'],
            'xmlValidationNodeId'           => $errorDetails['nodeId'],
        ];
    }

    private function exportInfo(string $folder, string $bookId, string $apiPrefix, ?string $extensionFilter = null
    ): array {
        $dir = rtrim($this->kernelRootDir, '/') . '/public/files/' . trim($folder, '/');

        // Create if missing
        if (!is_dir($dir)) {
            @mkdir($dir, 0777, true);
        }

        $name = $this->latestFilename($dir, "*{$bookId}*", $extensionFilter);
        $link = $name
            ? sprintf('%s/%s/%s/%s',
                rtrim($this->apiUrl, '/'),
                trim($apiPrefix, '/'),
                trim($folder, '/'),
                $name
            )
            : null;

        return ['name' => $name, 'link' => $link];
    }

    private function latestFilename(string $dir, string $pattern, ?string $extensionFilter = null): ?string
    {
        $files = glob($dir . '/' . ltrim($pattern, '/')) ?: [];

        if ($extensionFilter) {
            $files = array_filter($files, fn($file
            ) => pathinfo($file, PATHINFO_EXTENSION) === strtolower($extensionFilter));
        }

        if (!$files) {
            return null;
        }

        usort($files, static fn($a, $b) => filemtime($b) <=> filemtime($a));
        return basename($files[0]);
    }

    private function extractErrorDetails(?string $rawMessage): array
    {
        if ($rawMessage === null || $rawMessage === '') {
            return [
                'message' => null,
                'issues'  => null,
                'nodeId'  => null,
            ];
        }

        $decoded = json_decode($rawMessage, true);
        if (!is_array($decoded) || ($decoded['kind'] ?? null) !== 'xml_validation') {
            return [
                'message' => $rawMessage,
                'issues'  => null,
                'nodeId'  => null,
            ];
        }

        $issues = isset($decoded['issues']) && is_array($decoded['issues']) ? $decoded['issues'] : null;

        return [
            'message' => $decoded['message'] ?? null,
            'issues'  => $issues,
            'nodeId'  => $decoded['xmlValidationNodeId'] ?? null,
        ];
    }
}
