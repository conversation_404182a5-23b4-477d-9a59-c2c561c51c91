services:
  php:
    container_name: ${IMAGES_PREFIX:-}ct-api
    restart: unless-stopped
    # Increase memory limit to handle large XML exports
    mem_limit: 3g
    memswap_limit: 4g
    environment:
      SERVER_NAME: ${SERVER_NAME:-localhost}, php:80
      # Run "composer require symfony/orm-pack" to install and configure Doctrine ORM
      DATABASE_URL: mysql://app:12345@database:3306/app?serverVersion=12.0.2-MariaDB

      MERCURE_PUBLISHER_JWT_KEY: ${CADDY_MERCURE_JWT_SECRET:-!ChangeThisMercureHubJWTSecretKey!}
      MERCURE_SUBSCRIBER_JWT_KEY: ${CADDY_MERCURE_JWT_SECRET:-!ChangeThisMercureHubJWTSecretKey!}

      # Run "composer require symfony/mercure-bundle" to install and configure the Mercure integration
    #      MERCURE_URL: ${CADDY_MERCURE_URL:-http://php/.well-known/mercure}
    #      MERCURE_PUBLIC_URL: ${CADDY_MERCURE_PUBLIC_URL:-https://${SERVER_NAME:-localhost}:${HTTPS_PORT:-443}/.well-known/mercure}
    #      MERCURE_JWT_SECRET: ${CADDY_MERCURE_JWT_SECRET:-!ChangeThisMercureHubJWTSecretKey!}
    volumes:
      - caddy_data:/data
      - caddy_config:/config
    ports:
      - "${HTTP_PORT:-8000}:80/tcp" # HTTP
      - "${HTTPS_PORT:-8443}:443/tcp" # HTTPS
      - "${HTTP3_PORT:-8443}:443/udp" # HTTP/3
    depends_on:
      database:
        condition: service_healthy

  ###> doctrine/doctrine-bundle ###
  database:
    image: mariadb:12.0.2
    container_name: mariadb
    restart: unless-stopped
    environment:
      TZ: Etc/UTC
      MARIADB_ROOT_PASSWORD: root
      MARIADB_DATABASE: app
      MARIADB_USER: app
      MARIADB_PASSWORD: 12345
    ports:
      - "3307:3306"
    healthcheck:
      test: [ "CMD", "healthcheck.sh", "--connect", "--innodb_initialized" ]
      start_period: 10s
      interval: 10s
      timeout: 5s
      retries: 3
    volumes:
      # You may use a bind-mounted host directory instead, so that it is harder to accidentally remove the volume and lose all your data!
      #- ./frankenphp/db/data/:/var/lib/mysql:rw
      - database_data:/var/lib/mysql:rw
###< doctrine/doctrine-bundle ###

volumes:
  caddy_data:
  caddy_config:

  ###> symfony/mercure-bundle ###
  ###< symfony/mercure-bundle ###

  ###> doctrine/doctrine-bundle ###
  database_data:
###< doctrine/doctrine-bundle ###
