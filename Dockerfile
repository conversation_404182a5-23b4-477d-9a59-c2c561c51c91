#syntax=docker/dockerfile:1

# Versions
ARG PHP_VERSION=8.4
FROM dunglas/frankenphp:1-php${PHP_VERSION} AS frankenphp_upstream

# The different stages of this Dockerfile are meant to be built into separate images
# https://docs.docker.com/develop/develop-images/multistage-build/#stop-at-a-specific-build-stage
# https://docs.docker.com/compose/compose-file/#target



# Base FrankenPHP image
FROM frankenphp_upstream AS frankenphp_base

WORKDIR /app

VOLUME /app/var/

# persistent / runtime deps
# hadolint ignore=DL3008
RUN apt-get update && apt-get install -y --no-install-recommends \
	acl \
	file \
	gettext \
	git \
    nano \
	openssh-client \
	procps \
	supervisor \
	&& rm -rf /var/lib/apt/lists/*

RUN set -eux; \
	install-php-extensions \
		@composer \
		apcu \
		intl \
		gd \
		gmp \
		opcache \
		pdo_mysql \
		tidy \
		xsl \
		zip \
	;

# https://getcomposer.org/doc/03-cli.md#composer-allow-superuser
ENV COMPOSER_ALLOW_SUPERUSER=1

# Transport to use by <PERSON><PERSON><PERSON> (default to Bolt)
ENV MERCURE_TRANSPORT_URL=bolt:///data/mercure.db

ENV PHP_INI_SCAN_DIR=":$PHP_INI_DIR/app.conf.d"

###> recipes ###
###> doctrine/doctrine-bundle ###
#RUN install-php-extensions pdo_pgsql
###< doctrine/doctrine-bundle ###
###< recipes ###

COPY --link frankenphp/conf.d/10-app.ini $PHP_INI_DIR/app.conf.d/
COPY --link --chmod=755 frankenphp/docker-entrypoint.sh /usr/local/bin/docker-entrypoint
COPY --link frankenphp/Caddyfile /etc/frankenphp/Caddyfile

# supervisord
COPY --link frankenphp/supervisord.conf /etc/supervisor/supervisord.conf
COPY --link frankenphp/supervisord-app.conf /etc/supervisor/conf.d/supervisord-app.conf
RUN touch /var/run/supervisor.sock; \
    chmod 777 /var/run/supervisor.sock;

ENTRYPOINT ["docker-entrypoint"]

HEALTHCHECK --start-period=60s CMD curl -f http://localhost:2019/metrics || exit 1
CMD [ "frankenphp", "run", "--config", "/etc/frankenphp/Caddyfile" ]



# Dev FrankenPHP image
FROM frankenphp_base AS frankenphp_dev

ENV APP_ENV=${APP_ENV:-dev}
ENV XDEBUG_MODE=${XDEBUG_MODE:-off}
ENV FRANKENPHP_WORKER_CONFIG=watch

RUN mv "$PHP_INI_DIR/php.ini-development" "$PHP_INI_DIR/php.ini"

RUN set -eux; \
	install-php-extensions \
		xdebug \
	;

COPY --link frankenphp/conf.d/20-app.dev.ini $PHP_INI_DIR/app.conf.d/

CMD [ "frankenphp", "run", "--config", "/etc/frankenphp/Caddyfile", "--watch" ]



# Prod FrankenPHP image
FROM frankenphp_base AS frankenphp_prod

ENV APP_ENV=prod

RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

RUN --mount=type=secret,id=composer-auth \
    cp /run/secrets/composer-auth /app/auth.json

COPY --link frankenphp/conf.d/20-app.prod.ini $PHP_INI_DIR/app.conf.d/

# prevent the reinstallation of vendors at every changes in the source code
COPY --link composer.* symfony.* ./
RUN set -eux; \
	composer install --no-cache --prefer-dist --no-dev --no-autoloader --no-scripts --no-progress

# copy sources
COPY --link . ./
RUN rm -Rf frankenphp/

RUN set -eux; \
	mkdir -p var/cache var/log; \
	composer dump-autoload --classmap-authoritative --no-dev; \
	chmod +x bin/console; sync;

ARG APP_RUNTIME_ENV=dev
RUN --mount=type=secret,id=symfony-decryption-secret \
    export SYMFONY_DECRYPTION_SECRET="$(cat /run/secrets/symfony-decryption-secret)" && \
    [ -f .env.${APP_RUNTIME_ENV} ] && cp .env.${APP_RUNTIME_ENV} .env.local; \
    APP_RUNTIME_ENV=${APP_RUNTIME_ENV} php bin/console secrets:decrypt-to-local --force; \
    mv .env.${APP_RUNTIME_ENV}.local .env.${APP_ENV}.local;

RUN set -eux; \
	composer dump-env prod; \
    composer run-script --no-dev post-install-cmd;
