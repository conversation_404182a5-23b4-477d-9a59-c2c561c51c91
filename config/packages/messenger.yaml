framework:
  messenger:
    # reset services after consuming messages
    #    reset_on_message: true

    # Uncomment this (and the failed transport below) to send failed messages to this transport for later handling.
    failure_transport: failed

    transports:
      async_codebook:
        dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
        options:
          queue_name: async_codebook
        retry_strategy:
          max_retries: 0

      async_export:
        dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
        options:
          queue_name: async_export
        retry_strategy:
          max_retries: 0

      failed:
        dsn: 'doctrine://default?queue_name=failed'

    routing:
      # CODEBOOK
      App\Message\Cdp\SyncProposalsFromEndpointMessage: async_codebook
      App\Message\Project\Action\ReEvaluateWorkflowStatusMessage: async_codebook
      App\Message\CodeBook\UpdateNodesByXRefAsyncMessage: async_codebook
      App\Message\CodeBook\UpsertUrlLinksAsyncMessage: async_codebook
      App\Message\CodeBook\UpsertXRefLinksAsyncMessage: async_codebook
      App\Message\Project\CodeChanges\AssociateCodeChangesMessage: async_codebook

      # EXPORT
      App\Message\Project\BookPDFExportMessage: async_export
      App\Message\Project\BookRTFExportMessage: async_export
      App\Message\Project\BookXMLExportMessage: async_export
      App\Message\ChapterPdfVersionMessage: async_export

when@test:
  framework:
    messenger:
      transports:
        async_codebook: 'in-memory://'
        async_export: 'in-memory://'
