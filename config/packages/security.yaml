security:
  # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
  password_hashers:
    Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'

  # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
  providers:
    jwt:
      lexik_jwt:
        class: App\Entity\User\User
    cognito_users:
      id: App\Security\CognitoUserProvider
    test_users:
      id: App\Security\TestUserProvider
    all_users:
      chain:
        providers: [ 'test_users', 'cognito_users' ]

  firewalls:
    dev:
      pattern: ^/(_(profiler|wdt)|css|images|js)/
      security: false

    swagger_ui:
      pattern: ^/api/doc   # Update with your API documentation path
      security: false

    files:
      pattern: ^/api/janus/file
      security: false

    login:
      pattern: ^/api/login
      stateless: true
      json_login:
        check_path: /api/login_check
        success_handler: lexik_jwt_authentication.handler.authentication_success
        failure_handler: lexik_jwt_authentication.handler.authentication_failure
      provider: all_users
      custom_authenticators:
        - App\Security\TestAuthenticator
        - App\Security\CognitoAuthenticator
      entry_point: App\Security\CognitoAuthenticator

    api:
      pattern: ^/api
      stateless: true
      entry_point: jwt
      provider: jwt
      jwt: ~
      refresh_jwt:
        check_path: api_refresh_token

  role_hierarchy:
    ROLE_SECRETARIAT: ROLE_USER
    ROLE_PUBS: ROLE_USER
    ROLE_CODES: ROLE_SECRETARIAT
    ROLE_ADMIN: [ ROLE_SECRETARIAT, ROLE_PUBS, ROLE_CODES ]
    ROLE_SUPER_ADMIN: [ ROLE_ADMIN, ROLE_SECRETARIAT ]

  access_control:
    - { path: ^/api/file,           roles: PUBLIC_ACCESS }
    - { path: ^/athena/image,       roles: PUBLIC_ACCESS }
    - { path: ^/api/login,          roles: PUBLIC_ACCESS }
    - { path: ^/api/token/refresh,  roles: PUBLIC_ACCESS }
    - { path: ^/api,                roles: IS_AUTHENTICATED_FULLY }
    - { path: ^/docconversion/v1,   roles: PUBLIC_ACCESS }
    - { path: ^/,                   roles: PUBLIC_ACCESS }
