<?php
declare(strict_types=1);

namespace DoctrineMigrations;

use App\Service\Helper\PhpHelper;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

use function count;
use function mb_strpos;
use function sprintf;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20251118161109 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Fix publication-ids';
    }

    public function up(Schema $schema): void
    {
        PhpHelper::setUnlimitedLimits();

        $publicationIdMap = [
            'IBC2024P1' => 'IBC2027V1.0',
            'IFC2024P1' => 'IFC2027V1.0',
            'IECC2024P1' => 'IECC2027V1.0',
            'IFGC2024P1' => 'IFGC2027V1.0',
            'IBC2024V1.0' => 'IBC2027V1.0',
            'IRC2024P1' => 'IRC2027V1.0',
            'IMC2024P1' => 'IMC2027V1.0',
            'IFC2021P1' => 'IFC2027V1.0',
            'ICCPC2024P1' => 'ICCPC2027V1.0',
            'ICCPC2024V1.0' => 'ICCPC2027V1.0',
            'IPC2024P1' => 'IPC2027V1.0',
            'IPMC2024P1' => 'IPMC2027V1.0',
            'IEBC2024V1.0' => 'IEBC2027V1.0',
            'IBC2021P1' => 'IBC2027V1.0',
            'IWUIC2024P1' => 'IWUIC2027V1.0',
            'IWUIC2021P1' => 'IWUIC2027V1.0',
            'ISPSC2024V2.0' => 'ISPSC2027V1.0',
            'IPSDC2024V1' => 'IPSDC2027V1.0',
            'IPSDC2021P1' => 'IPSDC2027V1.0',
            'IFGC2024V1.0' => 'IFGC2027V1.0',
            'IEBC2024' => 'IEBC2027V1.0',
            'IEBC2024P1' => 'IEBC2027V1.0',
            'IPC2021P1' => 'IPC2027V1.0',
            'IMC2024V1.0' => 'IMC2027V1.0',
            'IMC2024' => 'IMC2027V1.0',
            'IBC2024' => 'IBC2027V1.0',
            'IFC2024V1.0' => 'IFC2027V1.0',
            'IPC2024V1.0' => 'IPC2027V1.0',
            'IPMC2024V1.0' => 'IPMC2027V1.0',
            'IPSDC2024V1.0' => 'IPSDC2027V1.0',
            'IECC2024V1.0' => 'IECC2027V1.0',
            'IMC2021P1' => 'IMC2027V1.0',
            'IECC2021P1' => 'IECC2027V1.0',
            'IRC2021P1' => 'IRC2027V1.0',
            'IPSDC2024P1' => 'IPSDC2027V1.0',
            'IECC2024D2RERE' => 'IECC2027V1.0',
            'IECC2024P1CE' => 'IECC2027V1.0',
            'IECC2024P1RE' => 'IECC2027V1.0',
            'ISPSC2024P1' => 'ISPSC2027V1.0',
            'IFG2024P1' => 'IFGC2027V1.0',
            'IPMC2021P1' => 'IPMC2027V1.0',
            'IZC2024P1' => 'IZC2027V1.0',
            'IPCC2021P1' => 'IPCC2027V1.0',
            'IEPC2024P1' => 'IEPC2027V1.0',
            'IEB2024P1' => 'IEBC2027V1.0',
            'IFC204P1' => 'IFC2027V1.0',
            'IEBC2021P1' => 'IEBC2027V1.0',
            'IEBC20245P1' => 'IEBC2027V1.0',
            'IRC2024V1.0' => 'IRC2027V1.0',
            'IRC2021V1.0' => 'IRC2027V1.0',
            'IFGC2021P1' => 'IFGC2027V1.0',
            'IPC2024_Ch06_Sec604.3' => 'IPC2027V1.0',
            'IPC20204P1' => 'IPC2027V1.0',
            'ICC2024P1' => 'IPC2027V1.0',
            'IECC2021V1.0' => 'IECC2027V1.0',
            'IEBC2021V1.0' => 'IEBC2027V1.0',
            'IBC2012' => 'IBC2027V1.0',
            'IBEC2024P1' => 'IEBC2027V1.0',
            'IMC2024P1_Ch13' => 'IMC2027V1.0',
            'ISPSC2021P1' => 'ISPSC2027V1.0',
        ];

        $tableFields = [
            'code_book_appendix'   => ['objectives', 'body'],
            'code_book_chapter'    => ['objectives', 'body'],
            'code_book_definition' => ['definition'],
            'code_book_figure'     => ['figure_notes'],
            'code_book_section'    => ['objectives', 'body'],
            'code_book_table'      => ['objectives', 'table', 'table_notes'],
        ];

        foreach ($tableFields as $table => $columns) {
            $records = $this->connection->fetchAllAssociative('SELECT * FROM ' . $table);

            foreach ($records as $record) {
                foreach ($columns as $column) {
                    if (empty($record[$column]) || false === mb_strpos($record[$column], '<')) {
                        continue;
                    }

                    $orig = $record[$column];
                    $val = $orig;

                    // Extract publication-ids
                    preg_match_all(
                        '/<publication-ref\b(?=[^>]*\brole="ICC-publication")(?=[^>]*\bpublication-id="([^"]+)")/i',
                        $val,
                        $matches
                    );

                    // Replace publication-ids
                    foreach ($publicationIdMap as $old => $new) {
                        $val = preg_replace(
                            sprintf('/(<publication-ref\b[^>]*publication-id=")%s(")/i', preg_quote($old, '/')),
                            '$1' . $new . '$2',
                            $val
                        );
                    }

                    // Update if changed
                    if ($val !== $orig) {
                        $this->addSql(
                            sprintf('UPDATE %s SET `%s` = :val WHERE id = :id', $table, $column),
                            [
                                'id'  => $record['id'],
                                'val' => $val,
                            ]
                        );
                    }
                }
            }

            $this->write(sprintf(
                'Processed %d rows in table "%s"',
                count($records),
                $table
            ));
        }
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
