<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Service\CodeXml\Mapper;

use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\Volume;
use App\Helper\Xml2Helper;
use App\ObjectMapper\CodeBookToXmlMapper;
use App\Serializer\Encoder\Xml2\Xml2Encoder;
use App\Service\CodeXml\CodeXmlNode;
use App\Service\CodeXml\Mapper\ChapterXmlMapper;

use function DeepCopy\deep_copy;
use function range;
use function sprintf;

class ChapterXmlMapperTest extends MapperTestCase
{
    private ChapterXmlMapper $mapper;
    private CodeBookToXmlMapper $codeBookToXmlMapper;
    private Xml2Encoder $xml2Encoder;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(ChapterXmlMapper::class);
        $this->codeBookToXmlMapper = self::getContainer()->get(CodeBookToXmlMapper::class);
        $this->xml2Encoder = self::getContainer()->get(Xml2Encoder::class);
    }

    /** @dataProvider levels */
    public function testLevels(Chapter $entity, int $level = 1): void
    {
        if ($level === 1) {
            $parent = new Volume();
        } else {
            $parent = new Chapter();
            $parent->setDisplayLevel($level - 1);
        }
        $parent->addChild($entity);

        $element = $this->codeBookToXmlMapper->map($parent);
        $xml = $this->xml2Encoder->encode($element);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $codeXmlNode = $this->mapper->map($entity, $document, $xpath);
        $domElement = $codeXmlNode->getDomElement();

        $this->assertNotNull($codeXmlNode);
        $this->assertSame($document, $domElement->ownerDocument);

        $expected = sprintf('<level-%d ct-uuid="entity" role="chapter"/>', $level);
        $this->assertXmlStringEqualsXmlString($expected, $domElement->ownerDocument->saveXML($domElement));
    }

    public static function levels(): iterable
    {
        $entity = new Chapter();
        $entity->setNodeId('');
        $entity->setUlid('entity');
        foreach (range(1, 11) as $level) {
            $entity->setDisplayLevel($level);
            yield 'level-' . $level => [deep_copy($entity), $level];
        }
    }

    /** @dataProvider attributeCases */
    public function testMapAttributes(Chapter $entity, array $attrs): void
    {
        $attrs['ct-uuid'] = 'entity';
        $entity->setUlid('entity');

        $parent = new Volume();
        $parent->addChild($entity);

        $element = $this->codeBookToXmlMapper->map($parent);
        $xml = $this->xml2Encoder->encode($element);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $codeXmlNode = $this->mapper->map($entity, $document, $xpath);
        $domElement = $codeXmlNode->getDomElement();

        $this->assertNotNull($codeXmlNode);
        $this->assertSame($document, $domElement->ownerDocument);

        $expected = sprintf('<level-1 %s/>', $this->arrayToAttrs($attrs));
        $this->assertXmlStringEqualsXmlString($expected, $domElement->ownerDocument->saveXML($domElement));
    }

    /** @dataProvider attributeCases */
    public function testSyncAttributes(Chapter $expected, array $attrs): void
    {
        $expected->setNodeId('expected');
        $expected->setUlid('expected');

        $actual = new Chapter();
        $actual->setNodeId('expected');
        $actual->setUlid('expected');

        $xml = '<chapter id="expected" ct-uuid="expected"/>';
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $domEl = $document->documentElement->firstElementChild;
        foreach ($attrs as $attr => $value) {
            $domEl->setAttribute($attr, $value);
        }

        $codeXmlNode = new CodeXmlNode($actual, $xml, $domEl);
        $this->mapper->syncEntity($codeXmlNode, $xpath);

        $this->assertTrue(empty($attrs) || $codeXmlNode->hasChanges());
        $this->assertEquals($expected, $actual);
    }

    public static function attributeCases(): iterable
    {
        $entity = new Chapter();
        $entity->setNodeId('');
        $attrs = ['role' => 'chapter'];
        yield 'baseline' => [deep_copy($entity), $attrs];

        yield from static::commonAttrCases($entity, $attrs);
        yield from static::revisionAttrCases($entity, $attrs);

        $entity->setIndexNumber('indexNumber');
        $attrs['indexnum'] = 'indexNumber';
        yield 'attrs / @indexnum' => [deep_copy($entity), $attrs];

        $entity->setReserveCount(100);
        $attrs['reservecount'] = '100';
        yield 'attrs / @reservecount' => [deep_copy($entity), $attrs];

        $entity->setTocEntry(true);
        $attrs['tocentry'] = 'yes';
        yield 'attrs / @tocentry' => [deep_copy($entity), $attrs];

        $entity->setTocAutoAdd(true);
        $attrs['tocautoadd'] = 'yes';
        yield 'attrs / @tocautoadd' => [deep_copy($entity), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testMapFields(Chapter $entity, string $innerXml): void
    {
        $parent = new Volume();
        $parent->addChild($entity);
        $entity->setNodeId('entity');
        $entity->setUlid('entity');

        $element = $this->codeBookToXmlMapper->map($parent);
        $xml = $this->xml2Encoder->encode($element);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $codeXmlNode = $this->mapper->map($entity, $document, $xpath);
        $domElement = $codeXmlNode->getDomElement();

        $this->assertNotNull($codeXmlNode);
        $this->assertSame($document, $domElement->ownerDocument);

        $expected = sprintf('<level-1 id="entity" ct-uuid="entity" role="chapter">%s</level-1>', $innerXml);
        $this->assertXmlStringEqualsXmlString($expected, $domElement->ownerDocument->saveXML($domElement));
    }

    /** @dataProvider fieldCases */
    public function testSyncFields(Chapter $expected, string $innerXml): void
    {
        $expected->setNodeId('expected');
        $expected->setUlid('expected');

        $actual = new Chapter();
        $actual->setNodeId('expected');
        $actual->setUlid('expected');

        $xml = sprintf('<level-1 id="expected" ct-uuid="expected" role="chapter">%s</level-1>', $innerXml);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $domEl = $document->documentElement->firstElementChild;
        $codeXmlNode = new CodeXmlNode($actual, '<level-1/>', $domEl);
        $this->mapper->syncEntity($codeXmlNode, $xpath);

        $this->assertTrue($codeXmlNode->hasChanges());
        $this->assertEquals($expected, $actual);
    }

    public static function fieldCases(): iterable
    {
        $entity = new Chapter();
        $innerXml = '';
        yield 'baseline' => [deep_copy($entity), $innerXml];

        yield from static::syncTitleGroupCase($entity, $innerXml);
        yield from static::syncQrCodeCase($entity, $innerXml);

        $entity->setRelocatedFrom('relocated from');
        $entity->setRelocatedFromId('relocatedFrom');
        $innerXml .= '<relocated-from rid="relocatedFrom">relocated from</relocated-from>';
        yield 'relocatedFrom' => [deep_copy($entity), $innerXml];

        $entity->setHistory('History');
        $innerXml .= '<history>History</history>';
        yield 'history' => [deep_copy($entity), $innerXml];

        $entity->setObjectives('<p>Objectives</p>');
        $entity->setObjectivesTitle('Title');
        $innerXml .= <<<XML
<objectives>
    <titlegroup>
        <title>Title</title>
    </titlegroup>
    <body>
        <p>Objectives</p>
    </body>
</objectives>
XML;
        yield 'objectives' => [deep_copy($entity), $innerXml];

        $entity->setAbstract('<p>Abstract</p>');
        $entity->setAbstractTitle('Title');
        $innerXml .= <<<XML
<abstract>
    <titlegroup>
        <title>Title</title>
    </titlegroup>
    <p>Abstract</p>
</abstract>
XML;
        yield 'abstract' => [deep_copy($entity), $innerXml];

        $entity->setKeywords('<keyword>Keyword</keyword>');
        $entity->setKeywordsTitle('Title');
        $innerXml .= <<<XML
<keywords>
    <titlegroup>
        <title>Title</title>
    </titlegroup>
    <keyword>Keyword</keyword>
</keywords>
XML;
        yield 'keywords' => [deep_copy($entity), $innerXml];

        $entity->setBody('<p>Body.</p><p>Body.</p><p>Body.</p>');
        $innerXml .= <<<XML
<body>
    <p>Body.</p><p>Body.</p><p>Body.</p>
</body>
XML;
        yield 'body' => [deep_copy($entity), $innerXml];
    }
}
