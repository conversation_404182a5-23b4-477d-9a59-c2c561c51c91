<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Service\CodeBook;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Appendix;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\Definition;
use App\Entity\CodeBook\Figure;
use App\Entity\CodeBook\IndexEntry;
use App\Entity\CodeBook\Promulgator;
use App\Entity\CodeBook\ReferenceStandard;
use App\Entity\CodeBook\SecondaryIndexEntry;
use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Table;
use App\Service\CodeBook\CodeBookPathIdService;
use App\Service\CodeBook\ReferenceStandardYearParser;
use DateTime;
use PHPUnit\Framework\TestCase;

class CodeBookPathIdServiceTest extends TestCase
{
    /**
     * @dataProvider appendixCases
     * @dataProvider chapterCases
     * @dataProvider definitionCases
     * @dataProvider figureCases
     * @dataProvider promulgatorCases
     * @dataProvider refStandardCases
     * @dataProvider sectionCases
     * @dataProvider tableCases
     * @dataProvider edgeCases
     * @dataProvider collisionCases
     */
    public function testUpdatePathId(AbstractCodeBookNode $node, string $expected): void
    {
        $service = new CodeBookPathIdService();
        $actual = $service->updatePathId($node);
        $this->assertSame($expected, $actual);
    }

    public static function appendixCases(): iterable
    {
        $node = new Appendix();
        $node->setOriginalNumber('A');
        $node->setNumber('A');
        $node->setDeletedDate(null);

        yield 'appendix / base' => [$node, 'AppxA'];

        $node = new Appendix();
        $node->setOriginalNumber('A');
        $node->setNumber('<delete>A</delete><insert>B</insert>');
        $node->setDeletedDate(null);

        yield 'appendix / modified' => [$node, 'AppxB'];

        $node = new Appendix();
        $node->setOriginalNumber('');
        $node->setNumber('<insert>C</insert>');
        $node->setDeletedDate(null);

        yield 'appendix / new' => [$node, 'AppxC'];

        $node = new Appendix();
        $node->setOriginalNumber('D');
        $node->setNumber('<delete>D</delete>');
        $node->setDeletedDate(new DateTime());

        yield 'appendix / deleted' => [$node, 'AppxD-deleted'];
    }

    public static function chapterCases(): iterable
    {
        $node = new Chapter();
        $node->setOriginalNumber('1');
        $node->setNumber('1');
        $node->setDeletedDate(null);

        yield 'chapter / base' => [$node, 'Ch01'];

        $node = new Chapter();
        $node->setOriginalNumber('1');
        $node->setNumber('<delete>1</delete><insert>2</insert>');
        $node->setDeletedDate(null);

        yield 'chapter / modified' => [$node, 'Ch02'];

        $node = new Chapter();
        $node->setOriginalNumber('');
        $node->setNumber('<insert>3</insert>');
        $node->setDeletedDate(null);

        yield 'chapter / new' => [$node, 'Ch03'];

        $node = new Chapter();
        $node->setOriginalNumber('4');
        $node->setNumber('<delete>4</delete>');
        $node->setDeletedDate(new DateTime());

        yield 'chapter / deleted' => [$node, 'Ch04-deleted'];

        $node = new Chapter();
        $node->setOriginalNumber('');
        $node->setNumber('111');
        $node->setDeletedDate(null);

        yield 'chapter / 100+' => [$node, 'Ch111'];
    }

    public static function definitionCases(): iterable
    {
        $node = new Definition();
        $node->setTerm('ACCEPTED ENGINEERING PRACTICE.');
        $node->setDeletedDate(null);

        yield 'definition / base' => [$node, 'DefACCEPTED_ENGINEERING_PRACTICE'];

        $node = new Definition();
        $node->setTerm('<delete>ACCESS COVER.</delete><insert>ACCESS (TO).</insert>');
        $node->setDeletedDate(null);

        yield 'definition / modified' => [$node, 'DefACCESS_TO'];

        $node = new Definition();
        $node->setTerm('<insert>ACCESSIBLE.</insert>');
        $node->setDeletedDate(null);

        yield 'definition / new' => [$node, 'DefACCESSIBLE'];

        $node = new Definition();
        $node->setTerm('<delete>AIR BREAK (Drainage System).</delete>');
        $node->setDeletedDate(new DateTime());

        yield 'definition / deleted' => [$node, 'DefAIR_BREAK_DRAINAGE_SYSTEM-deleted'];
    }

    public static function figureCases(): iterable
    {
        $node = new Figure();
        $node->setOriginalNumber('608.9.1');
        $node->setNumber('608.9.1');
        $node->setDeletedDate(null);

        yield 'figure / base' => [$node, 'Fig608.9.1'];

        $node = new Figure();
        $node->setOriginalNumber('1');
        $node->setNumber('<delete>306.3(A)</delete><insert>306.3(B)</insert>');
        $node->setDeletedDate(null);

        yield 'figure / modified' => [$node, 'Fig306.3_B'];

        $node = new Figure();
        $node->setOriginalNumber('');
        $node->setNumber('<insert data-changed="changed_current" revision="changed">301</insert>');
        $node->setDeletedDate(null);

        yield 'figure / new' => [$node, 'Fig301'];

        $node = new Figure();
        $node->setOriginalNumber('R804.3.1.3(2)');
        $node->setNumber('<delete>R804.3.1.3(2)</delete>');
        $node->setDeletedDate(new DateTime());

        yield 'figure / deleted' => [$node, 'FigR804.3.1.3_2-deleted'];
    }

    public static function promulgatorCases(): iterable
    {
        $node = new Promulgator();
        $node->setAcronym('<url href="www.ansi.org">ANSI</url>');
        $node->setDeletedDate(null);

        yield 'promulgator / base' => [$node, 'PromANSI'];

        $node = new Promulgator();
        $node->setAcronym('<url href="www.AISI.org"><delete>AISI</delete><insert>ANSI</insert></url>');
        $node->setDeletedDate(null);

        yield 'promulgator / modified' => [$node, 'PromANSI'];

        $node = new Promulgator();
        $node->setAcronym('<insert role="insert" data-changed="changed_level0" data-changed-in="IPC2024V1.0"><url href="www.AISI.org">AISI</url></insert>');
        $node->setDeletedDate(null);

        yield 'promulgator / new' => [$node, 'PromAISI'];

        $node = new Promulgator();
        $node->setAcronym('<url href="www.AISI.org"><delete>AISI</delete></url>');
        $node->setDeletedDate(new DateTime());

        yield 'promulgator / deleted' => [$node, 'PromAISI-deleted'];
    }

    public static function refStandardCases(): iterable
    {
        yield 'refStandard / base' => [static::createReferenceStandard('Z83.20—2016', 'Z83.20—2016'), 'RefStdZ83.20'];

        yield 'refStandard / modified' => [
            static::createReferenceStandard('Z83.20—2016', 'Z21.8—94(R2017)'),
            'RefStdZ21.8',
        ];

        yield 'refStandard / new' => [
            static::createReferenceStandard('', 'Z97.1—2015 (R2020)'),
            'RefStdZ97.1',
        ];

        yield 'refStandard / deleted' => [
            static::createReferenceStandard('Z97.1—2015 (R2020)', '<delete>Z97.1—2015 (R2020)</delete>', true),
            'RefStdZ97.1_2015_R2020-deleted',
        ];
    }

    private static function createReferenceStandard(string $originalNumber, string $currentNumber, bool $deleted = false
    ): ReferenceStandard {
        $node = new ReferenceStandard();
        $node->setOriginalNumber($originalNumber);
        [$number, $year] = ReferenceStandardYearParser::split($currentNumber);
        $node->setNumber($number);
        $node->setNumberYear($year ?? '');

        if ($deleted) {
            $node->setDeletedDate(new DateTime());
        }

        return $node;
    }

    public static function sectionCases(): iterable
    {
        $node = new Section();
        $node->setOriginalNumber('101');
        $node->setNumber('101');
        $node->setDeletedDate(null);

        yield 'section / base' => [$node, 'Sec101'];

        $node = new Section();
        $node->setOriginalNumber('102.8.2');
        $node->setNumber('<delete>102.8.1</delete><insert>102.8.2</insert>');
        $node->setDeletedDate(null);

        yield 'section / modified' => [$node, 'Sec102.8.2'];

        $node = new Section();
        $node->setOriginalNumber('');
        $node->setNumber('<insert role="insert" data-changed="changed_level0" data-changed-in="IPC2024V1.0">*********</insert>');
        $node->setDeletedDate(null);

        yield 'section / new' => [$node, 'Sec*********'];

        $node = new Section();
        $node->setOriginalNumber('915.2.1');
        $node->setNumber('<delete>915.2.1</delete>');
        $node->setDeletedDate(new DateTime());

        yield 'section / deleted' => [$node, 'Sec915.2.1-deleted'];

        $node = new Section();
        $node->setOriginalNumber('');
        $node->setNumber('');
        $node->setDeletedDate(null);
        $node->setPosition(11);

        yield 'section / position' => [$node, 'Sec11'];
    }

    public static function tableCases(): iterable
    {
        $node = new Table();
        $node->setOriginalNumber('101');
        $node->setNumber('101');
        $node->setDeletedDate(null);

        yield 'table / base' => [$node, 'Tbl101'];

        $node = new Table();
        $node->setOriginalNumber('102.8.2');
        $node->setNumber('<delete>102.8.1</delete><insert>102.8.2</insert>');
        $node->setDeletedDate(null);

        yield 'table / modified' => [$node, 'Tbl102.8.2'];

        $node = new Table();
        $node->setOriginalNumber('');
        $node->setNumber('<insert role="insert" data-changed="changed_level0" data-changed-in="IPC2024V1.0">*********</insert>');
        $node->setDeletedDate(null);

        yield 'table / new' => [$node, 'Tbl*********'];

        $node = new Table();
        $node->setOriginalNumber('915.2.1');
        $node->setNumber('<delete>915.2.1</delete>');
        $node->setDeletedDate(new DateTime());

        yield 'table / deleted' => [$node, 'Tbl915.2.1-deleted'];
    }

    public static function edgeCases(): iterable
    {
        $node = new Appendix();
        $node->setOriginalNumber('');
        $node->setNumber('<delete>E</delete>');
        $node->setDeletedDate(new DateTime());

        yield 'edge case / deleted no original' => [$node, 'AppxE-deleted'];
    }

    public static function collisionCases(): iterable
    {
        $parent = new Section();
        $parent->setLabel('Chapter');
        $parent->setNumber('4');
        $parent->setOriginalNumber('4');
        $parent->setDeletedDate(null);

        $appendix = new Section();
        $appendix->setLabel('Appendix');
        $appendix->setNumber('A');
        $appendix->setOriginalNumber('A');
        $appendix->setDeletedDate(null);
        $parent->addChild($appendix);

        $resource = new Section();
        $resource->setLabel('Resource');
        $resource->setNumber('A');
        $resource->setOriginalNumber('A');
        $resource->setDeletedDate(null);
        $parent->addChild($resource);

        yield 'section collision / appendix' => [$appendix, 'SecA_APPENDIX'];
        yield 'section collision / resource' => [$resource, 'SecA_RESOURCE'];

        $resourceDup = new Section();
        $resourceDup->setLabel('Resource');
        $resourceDup->setNumber('A');
        $resourceDup->setOriginalNumber('A');
        $resourceDup->setDeletedDate(null);
        $parent->addChild($resourceDup);

        yield 'section collision / resource duplicate' => [$resourceDup, 'SecA_RESOURCE_2'];

        $indexParent = new IndexEntry();
        $indexParent->setTerm('GUIDELINES FOR STRUCTURAL RETROFIT');
        $indexParent->setDeletedDate(null);

        $secondaryOne = new SecondaryIndexEntry();
        $secondaryOne->setTerm('SEISMIC');
        $secondaryOne->setDeletedDate(null);
        $indexParent->addChild($secondaryOne);

        $secondaryTwo = new SecondaryIndexEntry();
        $secondaryTwo->setTerm('SEISMIC');
        $secondaryTwo->setDeletedDate(null);
        $indexParent->addChild($secondaryTwo);

        yield 'secondary index collision / first' => [$secondaryOne, 'IndexSEISMIC_SECONDARYINDEXENTRY'];
        yield 'secondary index collision / second' => [$secondaryTwo, 'IndexSEISMIC_SECONDARYINDEXENTRY_2'];
    }
}
