<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBook\Action;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Appendix;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\Definition;
use App\Entity\CodeBook\Figure;
use App\Entity\CodeBook\Promulgator;
use App\Entity\CodeBook\ReferenceStandard;
use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Table;
use App\Entity\Project\CodeChange\AppendixCodeChange;
use App\Entity\Project\CodeChange\ChapterCodeChange;
use App\Entity\Project\CodeChange\DefinitionCodeChange;
use App\Entity\Project\CodeChange\FigureCodeChange;
use App\Entity\Project\CodeChange\ProjectCodeChange;
use App\Entity\Project\CodeChange\PromulgatorCodeChange;
use App\Entity\Project\CodeChange\ReferenceStandardCodeChange;
use App\Entity\Project\CodeChange\SectionCodeChange;
use App\Entity\Project\CodeChange\TableCodeChange;
use App\ObjectMapper\CodeBook\Action\AppendixActionMapper;
use App\ObjectMapper\CodeBook\Action\ChapterActionMapper;
use App\ObjectMapper\CodeBook\Action\DefinitionActionMapper;
use App\ObjectMapper\CodeBook\Action\FigureActionMapper;
use App\ObjectMapper\CodeBook\Action\PromulgatorActionMapper;
use App\ObjectMapper\CodeBook\Action\ProposalActionToCodeBookMapper;
use App\ObjectMapper\CodeBook\Action\ReferenceStandardActionMapper;
use App\ObjectMapper\CodeBook\Action\SectionActionMapper;
use App\ObjectMapper\CodeBook\Action\TableActionMapper;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class ProposalActionToCodeBookMapperTest extends TestCase
{
    private ProposalActionToCodeBookMapper $mapper;
    /** @var MockObject[] */
    private array $mappers = [];

    public function setUp(): void
    {
        $this->mappers = [
            'appendix'    => $this->createMock(AppendixActionMapper::class),
            'chapter'     => $this->createMock(ChapterActionMapper::class),
            'definition'  => $this->createMock(DefinitionActionMapper::class),
            'figure'      => $this->createMock(FigureActionMapper::class),
            'promulgator' => $this->createMock(PromulgatorActionMapper::class),
            'refStandard' => $this->createMock(ReferenceStandardActionMapper::class),
            'section'     => $this->createMock(SectionActionMapper::class),
            'table'       => $this->createMock(TableActionMapper::class),
        ];

        $this->mapper = new ProposalActionToCodeBookMapper(
            $this->mappers['appendix'],
            $this->mappers['chapter'],
            $this->mappers['definition'],
            $this->mappers['figure'],
            $this->mappers['promulgator'],
            $this->mappers['refStandard'],
            $this->mappers['section'],
            $this->mappers['table']
        );
    }

    /** @dataProvider iterations */
    public function testMap($toMock, ProjectCodeChange $action, string $mockClass): void
    {
        foreach ($this->mappers as $key => $i) {
            if ($key === $toMock) {
                $i->expects($this->once())
                  ->method('map')
                  ->with($action)
                  ->willReturn($this->createMock($mockClass));
            } else {
                $i->expects($this->never())->method('map');
            }
        }

        $this->mapper->map($action);
    }

    public static function iterations(): iterable
    {
        return [
            'appendix'    => ['appendix', new AppendixCodeChange(), Appendix::class],
            'chapter'     => ['chapter', new ChapterCodeChange(), Chapter::class],
            'definition'  => ['definition', new DefinitionCodeChange(), Definition::class],
            'figure'      => ['figure', new FigureCodeChange(), Figure::class],
            'promulgator' => ['promulgator', new PromulgatorCodeChange(), Promulgator::class],
            'refStandard' => ['refStandard', new ReferenceStandardCodeChange(), ReferenceStandard::class],
            'section'     => ['section', new SectionCodeChange(), Section::class],
            'table'       => ['table', new TableCodeChange(), Table::class],
        ];
    }
}
