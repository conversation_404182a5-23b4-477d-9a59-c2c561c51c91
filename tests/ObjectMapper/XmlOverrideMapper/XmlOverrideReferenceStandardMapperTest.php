<?php
declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlOverrideMapper;

use App\Entity\CodeBook;
use App\ObjectMapper\XmlOverrideMapper\XmlOverrideReferenceStandardMapper;
use App\Serializer\Encoder\Xml2\Element\Reference;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Element\Number;
use App\Serializer\Encoder\Xml2\Element\Title;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

/** @covers \App\ObjectMapper\XmlOverrideMapper\XmlOverrideReferenceStandardMapper */
class XmlOverrideReferenceStandardMapperTest extends KernelTestCase
{
    private ?XmlOverrideReferenceStandardMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlOverrideReferenceStandardMapper::class);
    }

    /**
     * @dataProvider mapCases
     */
    public function testMap(Reference $element, CodeBook\ReferenceStandard $expected): void
    {
        $entity = new CodeBook\ReferenceStandard();
        $entity->setUlid('uuid');
        $entity->setNodeId('nodeId');

        $actual = $this->mapper->map($element, $entity);

        $this->assertEquals($expected, $actual);
    }

    public static function mapCases(): iterable
    {
        yield 'base' => [static::createReference(), static::createExpected()];

        $numberElement = new Number();
        $numberElement->setBody('RS-101');
        $titleGroupWithNumber = new TitleGroup();
        $titleGroupWithNumber->setNumber($numberElement);
        $referenceWithNumber = static::createReference();
        $referenceWithNumber->setTitleGroup($titleGroupWithNumber);
        $expectedWithNumber = static::createExpected();
        $expectedWithNumber->setNumber('RS');
        $expectedWithNumber->setNumberYear('101');
        yield 'titleGroup-number' => [$referenceWithNumber, $expectedWithNumber];

        $titleOnlyGroup = new TitleGroup();
        $titleOnly = new Title();
        $titleOnly->setBody('Reference Standard Title');
        $titleOnlyGroup->setTitle($titleOnly);
        $referenceWithTitle = static::createReference();
        $referenceWithTitle->setTitleGroup($titleOnlyGroup);
        $expectedWithTitle = static::createExpected();
        $expectedWithTitle->setTitle('Reference Standard Title');
        yield 'titleGroup-title' => [$referenceWithTitle, $expectedWithTitle];

        $fullTitleGroup = new TitleGroup();
        $numberFull = new Number();
        $numberFull->setBody('RS-202');
        $titleFull = new Title();
        $titleFull->setBody('Updated Standard');
        $fullTitleGroup->setNumber($numberFull);
        $fullTitleGroup->setTitle($titleFull);
        $referenceFull = static::createReference();
        $referenceFull->setTitleGroup($fullTitleGroup);
        $expectedFull = static::createExpected();
        $expectedFull->setNumber('RS');
        $expectedFull->setNumberYear('202');
        $expectedFull->setTitle('Updated Standard');
        yield 'titleGroup-number+title' => [$referenceFull, $expectedFull];
    }

    private static function createReference(): Reference
    {
        return new Reference();
    }

    private static function createExpected(): CodeBook\ReferenceStandard
    {
        $expected = new CodeBook\ReferenceStandard();
        $expected->setUlid('uuid');
        $expected->setNodeId('nodeId');

        return $expected;
    }
}
