<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Enum\GoverningType;
use App\Enum\TitleType;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlVolumeMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlVolumeMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private CodeBookToXmlVolumeMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlVolumeMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\Volume $from, Xml2\Volume $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    public static function mapCases(): iterable
    {
        $from = new CodeBook\Volume();
        $from->setNodeId('from.volume');
        $from->setUlid('from.volume.uuid');

        $expected = new Xml2\Volume();
        $expected->setId('from.volume');
        $expected->setCtUuid('from.volume.uuid');
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from static::commonAttributeMapCases($from, $expected);
        yield from static::titleGroupMapCases($from, $expected);

        $from->setCustomerId('customerId');
        $expected->setCustomerId('customerId');
        yield 'customerId' => [deep_copy($from), deep_copy($expected)];

        $from->setTitleType(TitleType::CODE);
        $expected->setTitleType(TitleType::CODE);
        yield 'titleType' => [deep_copy($from), deep_copy($expected)];

        $from->setGoverningType(GoverningType::MUNICIPALITY);
        $expected->setGoverningType(GoverningType::MUNICIPALITY);
        yield 'governingType' => [deep_copy($from), deep_copy($expected)];

        $metadata = new Xml2\Metadata();
        $expected->setMetadata($metadata);

//        $from->setSchematronRuleset('schematronRuleset');
//        $metadata->addMeta('schematron-ruleset', 'schematronRuleset');
//        yield 'schematronRuleset' => [deep_copy($from), deep_copy($expected)];

        $from->setMetaTitle('metaTitle');
        $metadata->addMeta('title', 'metaTitle');
        yield 'metaTitle' => [deep_copy($from), deep_copy($expected)];

        $from->setEdition('edition');
        $metadata->addMeta('edition', 'edition');
        yield 'edition' => [deep_copy($from), deep_copy($expected)];

        $from->setParentDocument('parentDocument');
        $metadata->addMeta('parent-document', 'parentDocument');
        yield 'parentDocument' => [deep_copy($from), deep_copy($expected)];

        $from->setPublicationId('publicationId');
        $metadata->addMeta('publication-id', 'publicationId');
        yield 'publicationId' => [deep_copy($from), deep_copy($expected)];

        $from->setYear('year');
        $metadata->addMeta('year', 'year');
        yield 'year' => [deep_copy($from), deep_copy($expected)];

        $from->setPublicationAbbreviation('publicationAbbreviation');
        $metadata->addMeta('publication-abbrev', 'publicationAbbreviation');
        yield 'publicationAbbreviation' => [deep_copy($from), deep_copy($expected)];

        $from->setVersion('version');
        $metadata->addMeta('version', 'version');
        yield 'version' => [deep_copy($from), deep_copy($expected)];

//        $from->setOutputType('output-type');
//        $metadata->addMeta('output-type', 'output-type');
//        yield 'output-type' => [deep_copy($from), deep_copy($expected)];

        $from->setOrigin('origin');
        $metadata->addMeta('origin', 'origin');
        yield 'origin' => [deep_copy($from), deep_copy($expected)];

        $from->setDateOrigin('dateOrigin');
        $metadata->addMeta('date-origin', 'dateOrigin');
        yield 'dateOrigin' => [deep_copy($from), deep_copy($expected)];

        $from->setModifiedBy('modifiedBy');
        $metadata->addMeta('modified-by', 'modifiedBy');
        yield 'modifiedBy' => [deep_copy($from), deep_copy($expected)];

        $from->setDateUpdated('dateUpdated');
        $metadata->addMeta('date-updated', 'dateUpdated');
        yield 'dateUpdated' => [deep_copy($from), deep_copy($expected)];

        $from->setPdfFormat('pdf-format');
        $metadata->addMeta('pdfformat', 'pdf-format');
        yield 'pdfFormat' => [deep_copy($from), deep_copy($expected)];

        $frontMatter = new CodeBook\FrontMatter();
        $frontMatter->setNodeId('frontMatter');
        $frontMatter->setUlid('frontMatter.uuid');
        $from->setFrontMatter($frontMatter);

        $expectedFrontMatter = new Xml2\FrontMatter();
        $expectedFrontMatter->setId('frontMatter');
        $expectedFrontMatter->setCtUuid('frontMatter.uuid');
        $expected->setFrontMatter($expectedFrontMatter);
        yield 'frontMatter' => [deep_copy($from), deep_copy($expected)];

        $chapter1 = new CodeBook\Chapter();
        $chapter1->setRole('chapter');
        $chapter1->setNodeId('chapter1');
        $chapter1->setUlid('chapter1.uuid');
        $from->addChild($chapter1);

        $chapter2 = new CodeBook\Chapter();
        $chapter2->setRole('chapter');
        $chapter2->setNodeId('chapter2');
        $chapter2->setUlid('chapter2.uuid');
        $from->addChild($chapter2);

        $expectedChapter1 = new Xml2\Level();
        $expectedChapter1->setRole('chapter');
        $expectedChapter1->setId('chapter1');
        $expectedChapter1->setCtUuid('chapter1.uuid');
        $expected->addChild($expectedChapter1);

        $expectedChapter2 = new Xml2\Level();
        $expectedChapter2->setRole('chapter');
        $expectedChapter2->setId('chapter2');
        $expectedChapter2->setCtUuid('chapter2.uuid');
        $expected->addChild($expectedChapter2);
        yield 'children' => [deep_copy($from), deep_copy($expected)];

        $backMatter = new CodeBook\BackMatter();
        $backMatter->setNodeId('backMatter');
        $backMatter->setUlid('backMatter.uuid');
        $from->setBackMatter($backMatter);

        $expectedFrontMatter = new Xml2\BackMatter();
        $expectedFrontMatter->setId('backMatter');
        $expectedFrontMatter->setCtUuid('backMatter.uuid');
        $expected->setBackMatter($expectedFrontMatter);
        yield 'backMatter' => [deep_copy($from), deep_copy($expected)];
    }
}
