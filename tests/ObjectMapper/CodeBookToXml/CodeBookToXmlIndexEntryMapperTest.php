<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlIndexEntryMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

/**
 * @covers \App\ObjectMapper\CodeBookToXml\AbstractCodeBookToXmlMapper
 * @covers \App\ObjectMapper\CodeBookToXml\CodeBookToXmlIndexEntryMapper
 */
class CodeBookToXmlIndexEntryMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private CodeBookToXmlIndexEntryMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlIndexEntryMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\IndexEntry $from, Xml2\IndexEntry $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    public static function mapCases(): iterable
    {
        $from = new CodeBook\IndexEntry();
        $from->setNodeId('from.index');
        $from->setUlid('from.index.uuid');

        $expectedPrimary = new Xml2\PrimaryIndexEntry();
        $expectedPrimary->setId('from.index');
        $expectedPrimary->setCtUuid('from.index.uuid');

        $expected = new Xml2\IndexEntry();
        $expected->setId('');
        $expected->setPrimaryIndexEntry($expectedPrimary);
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        $from->setReferenceId('referenceId');
        $expectedPrimary->setReferenceId('referenceId');
        yield 'referenceId' => [deep_copy($from), deep_copy($expected)];

        $from->setTerm('term');
        $term = new Xml2\Term();
        $term->setBody('term');
        $expectedPrimary->setTerm($term);
        yield 'term' => [deep_copy($from), deep_copy($expected)];

        $from->setNavPointerGroup('navPointerGroup');
        $expectedPrimary->setNavPointerGroup('navPointerGroup');
        yield 'navPointerGroup' => [deep_copy($from), deep_copy($expected)];

        $childSection = new CodeBook\SecondaryIndexEntry();
        $childSection->setNodeId('from.secondary');
        $childSection->setUlid('from.secondary.uuid');
        $from->addChild($childSection);

        $expectedChild = new Xml2\SecondaryIndexEntry();
        $expectedChild->setId('from.secondary');
        $expectedChild->setCtUuid('from.secondary.uuid');
        $expected->setChildren([$expectedChild]);
        yield 'children' => [deep_copy($from), deep_copy($expected)];
    }
}
