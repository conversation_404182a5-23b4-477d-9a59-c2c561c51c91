<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlPromulgatorMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlPromulgatorMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private CodeBookToXmlPromulgatorMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlPromulgatorMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\Promulgator $from, Xml2\Promulgator $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    public static function mapCases(): iterable
    {
        $from = new CodeBook\Promulgator();
        $from->setNodeId('from.promulgator');
        $from->setUlid('fron.promulgator.uuid');

        $address = new Xml2\Address();

        $expected = new Xml2\Promulgator();
        $expected->setId('from.promulgator');
        $expected->setCtUuid('fron.promulgator.uuid');
        $expected->setAddress($address);
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from static::commonAttributeMapCases($from, $expected);
        yield from static::revisionAttributeMapCases($from, $expected);

        $from->setAcronym('acronym');
        $acronym = new Xml2\Acronym();
        $acronym->setBody('acronym');
        $expected->setAcronym($acronym);
        yield 'acronym' => [deep_copy($from), deep_copy($expected)];

        $from->setAddressLine('addressLine');
        $address->setAddressLine('addressLine');
        yield 'address / addressLine' => [deep_copy($from), deep_copy($expected)];

        $from->setOrganizationName('organizationName');
        $address->setOrganizationName('organizationName');
        yield 'address / organizationName' => [deep_copy($from), deep_copy($expected)];

        $from->setStreet('street');
        $address->setStreet('street');
        yield 'address / street' => [deep_copy($from), deep_copy($expected)];

        $from->setCity('city');
        $address->setCity('city');
        yield 'address / city' => [deep_copy($from), deep_copy($expected)];

        $from->setState('state');
        $address->setState('state');
        yield 'address / state' => [deep_copy($from), deep_copy($expected)];

        $from->setPostalCode('postalCode');
        $address->setPostalCode('postalCode');
        yield 'address / postalCode' => [deep_copy($from), deep_copy($expected)];

        $from->setCountry('country');
        $address->setCountry('country');
        yield 'address / country' => [deep_copy($from), deep_copy($expected)];

        $from->setInternational(true);
        $address->setInternational(true);
        yield 'address / international' => [deep_copy($from), deep_copy($expected)];

        $from->setUrl('url');
        $url = new Xml2\Url();
        $url->setBody('url');
        $address->setUrl($url);
        yield 'address / url' => [deep_copy($from), deep_copy($expected)];

        $from->setUrlHref('urlHref');
        $url->setHref('urlHref');
        yield 'address / url / href' => [deep_copy($from), deep_copy($expected)];

        $from->setUrlAlt('urlAlt');
        $url->setAlt('urlAlt');
        yield 'address / url / alt' => [deep_copy($from), deep_copy($expected)];

        $from->setEmail('email');
        $email = new Xml2\Email();
        $email->setBody('email');
        $address->setEmail($email);
        yield 'address / email' => [deep_copy($from), deep_copy($expected)];

        $from->setPhone('phone');
        $address->setPhone('phone');
        yield 'address / phone' => [deep_copy($from), deep_copy($expected)];

        $from->setFax('fax');
        $address->setFax('fax');
        yield 'address / fax' => [deep_copy($from), deep_copy($expected)];

        $expectedReferences = new Xml2\References();
        $expected->setReferences($expectedReferences);

        $childReferenceStandard = new CodeBook\ReferenceStandard();
        $childReferenceStandard->setNodeId('code.referenceStandard1');
        $childReferenceStandard->setUlid('code.referenceStandard1.uuid');
        $from->addChild($childReferenceStandard);

        $expectedReference = new Xml2\Reference();
        $expectedReference->setId('code.referenceStandard1');
        $expectedReference->setCtUuid('code.referenceStandard1.uuid');
        $expectedReference->setTitleGroup(new Xml2\TitleGroup());
        $expectedReferences->addChild($expectedReference);
        yield 'referenceStandard1' => [deep_copy($from), deep_copy($expected)];

        $childReferenceStandard = new CodeBook\ReferenceStandard();
        $childReferenceStandard->setNodeId('code.referenceStandard2');
        $childReferenceStandard->setUlid('code.referenceStandard2.uuid');
        $from->addChild($childReferenceStandard);

        $expectedReference = new Xml2\Reference();
        $expectedReference->setId('code.referenceStandard2');
        $expectedReference->setCtUuid('code.referenceStandard2.uuid');
        $expectedReference->setTitleGroup(new Xml2\TitleGroup());
        $expectedReferences->addChild($expectedReference);
        yield 'referenceStandard2' => [deep_copy($from), deep_copy($expected)];
    }
}
