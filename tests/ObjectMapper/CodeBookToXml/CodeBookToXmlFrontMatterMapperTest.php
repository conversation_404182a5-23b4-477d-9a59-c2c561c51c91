<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlFrontMatterMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlFrontMatterMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private CodeBookToXmlFrontMatterMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlFrontMatterMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\FrontMatter $from, Xml2\FrontMatter $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    public static function mapCases(): iterable
    {
        $from = new CodeBook\FrontMatter();
        $from->setNodeId('from.frontMatter');
        $from->setUlid('from.frontMatter.uuid');

        $expected = new Xml2\FrontMatter();
        $expected->setId('from.frontMatter');
        $expected->setCtUuid('from.frontMatter.uuid');
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from static::commonAttributeMapCases($from, $expected);

        $codeTitlePage = new CodeBook\TitlePage();
        $codeTitlePage->setNodeId('from.titlePage');
        $codeTitlePage->setUlid('from.titlePage.uuid');
        $from->setTitlePage($codeTitlePage);

        $expectedTitlePage = new Xml2\TitlePage();
        $expectedTitlePage->setId('from.titlePage');
        $expectedTitlePage->setCtUuid('from.titlePage.uuid');
        $expected->setTitlePage($expectedTitlePage);
        yield 'titlePage' => [deep_copy($from), deep_copy($expected)];

        $codeCopyrightPage = new CodeBook\CopyrightPage();
        $codeCopyrightPage->setNodeId('from.copyrightPage');
        $codeCopyrightPage->setUlid('from.copyrightPage.uuid');
        $from->setCopyrightPage($codeCopyrightPage);

        $expectedCopyrightPage = new Xml2\CopyrightPage();
        $expectedCopyrightPage->setId('from.copyrightPage');
        $expectedCopyrightPage->setCtUuid('from.copyrightPage.uuid');
        $expected->setCopyrightPage($expectedCopyrightPage);
        yield 'copyrightPage' => [deep_copy($from), deep_copy($expected)];

        $codePublisherNote = new CodeBook\PublisherNote();
        $codePublisherNote->setNodeId('from.publisherNote');
        $codePublisherNote->setUlid('from.publisherNote.uuid');
        $from->setPublisherNote($codePublisherNote);

        $expectedPublisherNote = new Xml2\PublisherNote();
        $expectedPublisherNote->setId('from.publisherNote');
        $expectedPublisherNote->setCtUuid('from.publisherNote.uuid');
        $expected->setPublisherNote($expectedPublisherNote);
        yield 'publisherNote' => [deep_copy($from), deep_copy($expected)];

        $codeForeword = new CodeBook\Foreword();
        $codeForeword->setNodeId('from.foreword');
        $codeForeword->setUlid('from.foreword.uuid');
        $from->setForeword($codeForeword);

        $expectedForeword = new Xml2\Foreword();
        $expectedForeword->setId('from.foreword');
        $expectedForeword->setCtUuid('from.foreword.uuid');
        $expected->setForeword($expectedForeword);
        yield 'foreword' => [deep_copy($from), deep_copy($expected)];

        $codePreface = new CodeBook\Preface();
        $codePreface->setNodeId('from.preface');
        $codePreface->setUlid('from.preface.uuid');
        $from->setPreface($codePreface);

        $expectedPreface = new Xml2\Preface();
        $expectedPreface->setId('from.preface');
        $expectedPreface->setCtUuid('from.preface.uuid');
        $expected->setPreface($expectedPreface);
        yield 'preface' => [deep_copy($from), deep_copy($expected)];

        $section = new CodeBook\Section();
        $section->setNodeId('from.section1');
        $section->setUlid('from.section1.uuid');
        $from->addChild($section);

        $expectedSection = new Xml2\Section();
        $expectedSection->setId('from.section1');
        $expectedSection->setCtUuid('from.section1.uuid');
        $expected->addChild($expectedSection);
        yield 'section1' => [deep_copy($from), deep_copy($expected)];

        $section = new CodeBook\Section();
        $section->setNodeId('from.section2');
        $section->setUlid('from.section2.uuid');
        $from->addChild($section);

        $expectedSection = new Xml2\Section();
        $expectedSection->setId('from.section2');
        $expectedSection->setCtUuid('from.section2.uuid');
        $expected->addChild($expectedSection);
        yield 'section2' => [deep_copy($from), deep_copy($expected)];
    }
}
