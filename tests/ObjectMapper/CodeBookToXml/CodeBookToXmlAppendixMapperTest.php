<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlAppendixMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlAppendixMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private CodeBookToXmlAppendixMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlAppendixMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\Appendix $from, Xml2\Appendix $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    public static function mapCases(): iterable
    {
        $from = new CodeBook\Appendix();
        $from->setNodeId('');
        $from->setUlid('');
        $expected = new Xml2\Appendix();
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from static::commonAttributeMapCases($from, $expected);
        yield from static::revisionAttributeMapCases($from, $expected);
        yield from static::titleGroupMapCases($from, $expected);
        yield from static::qrCodeMapCases($from, $expected);

        $from->setIndexNumber('indexNumber');
        $expected->setIndexNumber('indexNumber');
        yield '@indexNumber' => [deep_copy($from), deep_copy($expected)];

        $from->setTocEntry(true);
        $expected->setTocEntry(true);
        yield '@tocEntry' => [deep_copy($from), deep_copy($expected)];

        $from->setTocAutoAdd(true);
        $expected->setTocAutoAdd(true);
        yield '@tocAutoAdd' => [deep_copy($from), deep_copy($expected)];

        $from->setObjectives('objectives');
        $body = new Xml2\Body();
        $body->setBody('objectives');
        $objectives = new Xml2\Objectives();
        $objectives->setBody($body);
        $expected->setObjectives($objectives);
        yield 'objectives' => [deep_copy($from), deep_copy($expected)];

        $from->setObjectivesTitle('objectivesTitle');
        $title = new Xml2\Title();
        $title->setBody('objectivesTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $objectives->setTitleGroup($titleGroup);
        yield 'objectivesTitle' => [deep_copy($from), deep_copy($expected)];

        $from->setHistory('history');
        $history = new Xml2\History();
        $history->setBody('history');
        $expected->setHistory($history);
        yield 'history' => [deep_copy($from), deep_copy($expected)];

        $from->setNote('note');
        $note = new Xml2\Note();
        $note->setBody('note');
        $expected->setNote($note);
        yield 'note' => [deep_copy($from), deep_copy($expected)];

        $from->setNoteTitle('noteTitle');
        $title = new Xml2\Title();
        $title->setBody('noteTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $note->setTitleGroup($titleGroup);
        yield 'noteTitle' => [deep_copy($from), deep_copy($expected)];

        $from->setAbstract('abstract');
        $abstract = new Xml2\AbstractField();
        $abstract->setBody('abstract');
        $expected->setAbstract($abstract);
        yield 'abstract' => [deep_copy($from), deep_copy($expected)];

        $from->setAbstractTitle('abstractTitle');
        $title = new Xml2\Title();
        $title->setBody('abstractTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $abstract->setTitleGroup($titleGroup);
        yield 'abstractTitle' => [deep_copy($from), deep_copy($expected)];

        $from->setKeywords('keywords');
        $keywords = new Xml2\Keywords();
        $keywords->setBody('keywords');
        $expected->setKeywords($keywords);
        yield 'keywords' => [deep_copy($from), deep_copy($expected)];

        $from->setKeywordsTitle('keywordsTitle');
        $title = new Xml2\Title();
        $title->setBody('keywordsTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $keywords->setTitleGroup($titleGroup);
        yield 'keywordsTitle' => [deep_copy($from), deep_copy($expected)];

        $from->setBody('body');
        $sectionBody = new Xml2\SectionBody();
        $sectionBody->setBody('body');
        $expected->setBody($sectionBody);
        yield 'body' => [deep_copy($from), deep_copy($expected)];

        $fromSection = new CodeBook\Section();
        $from->addChild($fromSection);

        $expectedSection = new Xml2\Section();
        $expectedSection->setId($fromSection->getUlid());
        $expectedSection->setCtUuid($fromSection->getUlid());
        $expected->addChild($expectedSection);
        yield 'section' => [deep_copy($from), deep_copy($expected)];

        $fromRelocatedTo = new CodeBook\RelocatedTo();
        $from->addChild($fromRelocatedTo);

        $expectedRelocatedTo = new Xml2\RelocatedTo();
        $expectedRelocatedTo->setId($fromRelocatedTo->getUlid());
        $expectedRelocatedTo->setCtUuid($fromRelocatedTo->getUlid());
        $expected->addChild($expectedRelocatedTo);
        yield 'relocatedTo' => [deep_copy($from), deep_copy($expected)];
    }
}
