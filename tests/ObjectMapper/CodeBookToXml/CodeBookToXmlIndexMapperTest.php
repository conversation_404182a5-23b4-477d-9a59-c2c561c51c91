<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlIndexMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlIndexMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private CodeBookToXmlIndexMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlIndexMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\Index $from, Xml2\Index $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    public static function mapCases(): iterable
    {
        $from = new CodeBook\Index();
        $from->setNodeId('from.index');
        $from->setUlid('from.index.uuid');

        $expected = new Xml2\Index();
        $expected->setId('from.index');
        $expected->setCtUuid('from.index.uuid');
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from static::commonAttributeMapCases($from, $expected);
        yield from static::revisionAttributeMapCases($from, $expected);
        yield from static::titleGroupMapCases($from, $expected);

        $from->setTocEntry(true);
        $expected->setTocEntry(true);
        yield '@tocEntry' => [deep_copy($from), deep_copy($expected)];

        $fromEntry = new CodeBook\IndexEntry();
        $fromEntry->setNodeId('code.entry');
        $fromEntry->setUlid('code.entry.uuid');
        $from->addChild($fromEntry);

        $expectedPrimary = new Xml2\PrimaryIndexEntry();
        $expectedPrimary->setId('code.entry');
        $expectedPrimary->setCtUuid('code.entry.uuid');

        $expectedEntry = new Xml2\IndexEntry();
        $expectedEntry->setPrimaryIndexEntry($expectedPrimary);
        $expected->addChild($expectedEntry);
        yield 'index-entry' => [deep_copy($from), deep_copy($expected)];

        $fromDivision = new CodeBook\IndexDivision();
        $fromDivision->setNodeId('code.div');
        $fromDivision->setUlid('code.div.uuid');
        $from->addChild($fromDivision);

        $expectedDivision = new Xml2\IndexDivision();
        $expectedDivision->setId('code.div');
        $expectedDivision->setCtUuid('code.div.uuid');
        $expected->addChild($expectedDivision);
        yield 'index-div' => [deep_copy($from), deep_copy($expected)];
    }
}
