<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlPublicationMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlPublicationMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private CodeBookToXmlPublicationMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlPublicationMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\Publication $from, Xml2\Publication $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    public static function mapCases(): iterable
    {
        $from = new CodeBook\Publication();
        $from->setNodeId('from');
        $from->setUlid('from.uuid');
        $expected = new Xml2\Publication();
        $expected->setId('from');
        $expected->setCtUuid('from.uuid');
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from static::commonAttributeMapCases($from, $expected);

        $volume1 = new CodeBook\Volume();
        $volume1->setNodeId('volume1');
        $volume1->setUlid('volume1.uuid');
        $from->addChild($volume1);

        $volume2 = new CodeBook\Volume();
        $volume2->setNodeId('volume2');
        $volume2->setUlid('volume2.uuid');
        $from->addChild($volume2);

        $expectedVolume1 = new Xml2\Volume();
        $expectedVolume1->setId('volume1');
        $expectedVolume1->setCtUuid('volume1.uuid');

        $expectedVolume2 = new Xml2\Volume();
        $expectedVolume2->setId('volume2');
        $expectedVolume2->setCtUuid('volume2.uuid');

        $expected->setChildren([$expectedVolume1, $expectedVolume2]);
        yield 'children' => [deep_copy($from), deep_copy($expected)];
    }
}
