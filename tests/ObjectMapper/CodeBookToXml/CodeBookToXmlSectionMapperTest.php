<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlSectionMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlSectionMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlSectionMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlSectionMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\Section $from, Xml2\Section $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    public static function mapCases(): iterable
    {
        $from = new CodeBook\Section();
        $from->setNodeId('from.section');
        $from->setUlid('fron.section.uuid');
        $expected = new Xml2\Section();
        $expected->setId('from.section');
        $expected->setCtUuid('fron.section.uuid');
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from static::commonAttributeMapCases($from, $expected);
        yield from static::revisionAttributeMapCases($from, $expected);
        yield from static::titleGroupMapCases($from, $expected);

        $from->setIndexNumber('indexNumber');
        $expected->setIndexNumber('indexNumber');
        yield 'indexNumber' => [deep_copy($from), deep_copy($expected)];

        $from->setTocEntry(true);
        $expected->setTocEntry(true);
        yield 'tocEntry' => [deep_copy($from), deep_copy($expected)];

        $from->setReserveCount(100);
        $expected->setReserveCount(100);
        yield 'reserveCount' => [deep_copy($from), deep_copy($expected)];

        $from->setDisplayLevel(100);
        $expected->setDisplayLevel(100);
        yield 'displayLevel' => [deep_copy($from), deep_copy($expected)];

        $from->setBody('body');
        $sectionBody = new Xml2\SectionBody();
        $sectionBody->setBody('body');
        $expected->setBody($sectionBody);
        yield 'body' => [deep_copy($from), deep_copy($expected)];

        $from->setAbstract('abstract');
        $abstract = new Xml2\AbstractField();
        $abstract->setBody('abstract');
        $expected->setAbstract($abstract);
        yield 'abstract' => [deep_copy($from), deep_copy($expected)];

        $from->setAbstractTitle('abstractTitle');
        $title = new Xml2\Title();
        $title->setBody('abstractTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $abstract->setTitleGroup($titleGroup);
        yield 'abstractTitle' => [deep_copy($from), deep_copy($expected)];

        $from->setKeywords('keywords');
        $keywords = new Xml2\Keywords();
        $keywords->setBody('keywords');
        $expected->setKeywords($keywords);
        yield 'keywords' => [deep_copy($from), deep_copy($expected)];

        $from->setKeywordsTitle('keywordsTitle');
        $title = new Xml2\Title();
        $title->setBody('keywordsTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $keywords->setTitleGroup($titleGroup);
        yield 'keywordsTitle' => [deep_copy($from), deep_copy($expected)];

        $childSection1 = new CodeBook\Section();
        $childSection1->setNodeId('child.node');
        $childSection1->setUlid('child.uuid');
        $from->addChild($childSection1);

        $expectedChild1 = new Xml2\Section();
        $expectedChild1->setId('child.node');
        $expectedChild1->setCtUuid('child.uuid');
        $expected->addChild($expectedChild1);
        yield 'child1' => [deep_copy($from), deep_copy($expected)];

        $childSection2 = new CodeBook\Section();
        $childSection2->setNodeId('child.node');
        $childSection2->setUlid('child.uuid');
        $from->addChild($childSection2);

        $expectedChild2 = new Xml2\Section();
        $expectedChild2->setId('child.node');
        $expectedChild2->setCtUuid('child.uuid');
        $expected->addChild($expectedChild2);
        yield 'child2' => [deep_copy($from), deep_copy($expected)];
    }
}
