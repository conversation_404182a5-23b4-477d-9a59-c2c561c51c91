<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Enum\FloatEnum;
use App\Enum\Frame;
use App\Enum\Orientation;
use App\Enum\Rules;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlTableMapper;
use App\ObjectMapper\CodeBookToXmlMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlTableMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlTableMapper $mapper;
    private CodeBookToXmlMapper $codeBookToXmlMapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlTableMapper::class);
        $this->codeBookToXmlMapper = self::getContainer()->get(CodeBookToXmlMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\Table $from, Xml2\Table $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    public static function mapCases(): iterable
    {
        $from = new CodeBook\Table();
        $from->setNodeId('from.table');
        $from->setUlid('fron.table.uuid');
        $expected = new Xml2\Table();
        $expected->setId('from.table');
        $expected->setCtUuid('fron.table.uuid');
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from static::commonAttributeMapCases($from, $expected);
        yield from static::revisionAttributeMapCases($from, $expected);
        yield from static::titleGroupMapCases($from, $expected);
        yield from static::qrCodeMapCases($from, $expected);

        $from->setOrientation(Orientation::LANDSCAPE);
        $expected->setOrientation(Orientation::LANDSCAPE);
        yield 'attrs / @orientation' => [deep_copy($from), deep_copy($expected)];

        $from->setFloat(FloatEnum::MARGIN);
        $expected->setFloat(FloatEnum::MARGIN);
        yield 'attrs / @float' => [deep_copy($from), deep_copy($expected)];

        $from->setTocEntry(true);
        $expected->setTocEntry(true);
        yield 'attrs / @tocEntry' => [deep_copy($from), deep_copy($expected)];

        $from->setPageWide(true);
        $expected->setPageWide(true);
        yield 'attrs / @pageWide' => [deep_copy($from), deep_copy($expected)];

        $from->setFrame(Frame::TOP);
        $expected->setFrame(Frame::TOP);
        yield 'attrs / @frame' => [deep_copy($from), deep_copy($expected)];

        $from->setColumnSeparator(true);
        $expected->setColumnSeparator(true);
        yield 'attrs / @columnSeparator' => [deep_copy($from), deep_copy($expected)];

        $from->setRowSeparator(true);
        $expected->setRowSeparator(true);
        yield 'attrs / @rowSeparator' => [deep_copy($from), deep_copy($expected)];

        $from->setBackgroundColor('backgroundColor');
        $expected->setBackgroundColor('backgroundColor');
        yield 'attrs / @backgroundColor' => [deep_copy($from), deep_copy($expected)];

        $from->setTableStyle('tableStyle');
        $expected->setTableStyle('tableStyle');
        yield 'attrs / @tableStyle' => [deep_copy($from), deep_copy($expected)];

        $from->setClass('class');
        $expected->setClass('class');
        yield 'attrs / @class' => [deep_copy($from), deep_copy($expected)];

        $from->setTitleAttr('titleAttr');
        $expected->setTitleAttr('titleAttr');
        yield 'attrs / @titleAttr' => [deep_copy($from), deep_copy($expected)];

        $from->setSummary('summary');
        $expected->setSummary('summary');
        yield 'attrs / @summary' => [deep_copy($from), deep_copy($expected)];

        $from->setWidth('width');
        $expected->setWidth('width');
        yield 'attrs / @width' => [deep_copy($from), deep_copy($expected)];

        $from->setBorder('border');
        $expected->setBorder('border');
        yield 'attrs / @border' => [deep_copy($from), deep_copy($expected)];

        $from->setCellPadding(100);
        $expected->setCellPadding(100);
        yield 'attrs / @cellPadding' => [deep_copy($from), deep_copy($expected)];

        $from->setCellSpacing(100);
        $expected->setCellSpacing(100);
        yield 'attrs / @cellSpacing' => [deep_copy($from), deep_copy($expected)];

        $from->setRules(Rules::COLS);
        $expected->setRules(Rules::COLS);
        yield 'attrs / @rules' => [deep_copy($from), deep_copy($expected)];

        $from->setTable('body');
        $expected->setBody('body');
        yield 'body' => [deep_copy($from), deep_copy($expected)];

        $from->setCaption('caption');
        $caption = new Xml2\Caption();
        $caption->setBody('caption');
        $expected->setCaption($caption);
        yield 'caption' => [deep_copy($from), deep_copy($expected)];

        $from->setLegend('legend');
        $expected->setLegend('legend');
        yield 'legend' => [deep_copy($from), deep_copy($expected)];

        $from->setTableNotes('tableNotes');
        $tableNotes = new Xml2\TableNotes();
        $tableNotes->setBody('tableNotes');
        $expected->setTableNotes($tableNotes);
        yield 'tableNotes' => [deep_copy($from), deep_copy($expected)];

        $from->setTableNotesTitle('tableNotesTitle');
        $title = new Xml2\Title();
        $title->setBody('tableNotesTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $tableNotes->setTitleGroup($titleGroup);
        yield 'tableNotesTitle' => [deep_copy($from), deep_copy($expected)];

        $from->setSource('source');
        $source = new Xml2\Source();
        $source->setBody('source');
        $expected->setSource($source);
        yield 'source' => [deep_copy($from), deep_copy($expected)];

        $from->setCredit('credit');
        $credit = new Xml2\Credit();
        $credit->setBody('credit');
        $expected->setCredit($credit);
        yield 'credit' => [deep_copy($from), deep_copy($expected)];

        $from->setCreditTitle('creditTitle');
        $title = new Xml2\Title();
        $title->setBody('creditTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $credit->setTitleGroup($titleGroup);
        yield 'creditTitle' => [deep_copy($from), deep_copy($expected)];
    }

    public function testFigureInSection(): void
    {
        $from = new CodeBook\Section();
        $from->setNodeId('from.section');
        $from->setUlid('fron.section.uuid');
        $expected = new Xml2\Section();
        $expected->setId('from.section');
        $expected->setCtUuid('fron.section.uuid');

        $table = new CodeBook\Table();
        $table->setNodeId('from.table');
        $table->setUlid('from.table.uuid');
        $from->addChild($table);

        $expectedTable = new Xml2\Table();
        $expectedTable->setId('from.table');
        $expectedTable->setCtUuid('from.table.uuid');
        $expected->addChild($expectedTable);

        $actual = $this->codeBookToXmlMapper->map($from);
        $this->assertEquals($expected, $actual);
    }
}
