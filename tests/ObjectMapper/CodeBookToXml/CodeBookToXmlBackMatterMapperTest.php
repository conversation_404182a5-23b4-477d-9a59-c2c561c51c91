<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlBackMatterMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlBackMatterMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private CodeBookToXmlBackMatterMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlBackMatterMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\BackMatter $from, Xml2\BackMatter $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    public static function mapCases(): iterable
    {
        $from = new CodeBook\BackMatter();
        $from->setNodeId('from.backMatter');
        $from->setUlid('from.backMatter.uuid');

        $expected = new Xml2\BackMatter();
        $expected->setId('from.backMatter');
        $expected->setCtUuid('from.backMatter.uuid');
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from static::commonAttributeMapCases($from, $expected);

        $fromAppendix = new CodeBook\Appendix();
        $fromAppendix->setNodeId('from.appendix');
        $fromAppendix->setUlid('from.appendix.uuid');
        $from->addChild($fromAppendix);

        $expectedAppendix = new Xml2\Appendix();
        $expectedAppendix->setId('from.appendix');
        $expectedAppendix->setCtUuid('from.appendix.uuid');
        $expected->addChild($expectedAppendix);
        yield 'appendix' => [deep_copy($from), deep_copy($expected)];

        $fromSection = new CodeBook\Section();
        $fromSection->setNodeId('from.section');
        $fromSection->setUlid('from.section.uuid');
        $from->addChild($fromSection);

        $expectedSection = new Xml2\Section();
        $expectedSection->setId('from.section');
        $expectedSection->setCtUuid('from.section.uuid');
        $expected->addChild($expectedSection);
        yield 'section' => [deep_copy($from), deep_copy($expected)];

        $fromIndex = new CodeBook\Index();
        $fromIndex->setNodeId('from.index');
        $fromIndex->setUlid('from.index.uuid');
        $from->addChild($fromIndex);

        $expectedIndex = new Xml2\Index();
        $expectedIndex->setId('from.index');
        $expectedIndex->setCtUuid('from.index.uuid');
        $expected->addChild($expectedIndex);
        yield 'index' => [deep_copy($from), deep_copy($expected)];
    }
}
