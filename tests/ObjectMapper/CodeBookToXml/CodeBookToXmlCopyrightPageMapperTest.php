<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlCopyrightPageMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlCopyrightPageMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private CodeBookToXmlCopyrightPageMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlCopyrightPageMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\CopyrightPage $from, Xml2\CopyrightPage $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    public static function mapCases(): iterable
    {
        $from = new CodeBook\CopyrightPage();
        $from->setNodeId('from.copyrightPage');
        $from->setUlid('from.copyrightPage.uuid');
        $expected = new Xml2\CopyrightPage();
        $expected->setId('from.copyrightPage');
        $expected->setCtUuid('from.copyrightPage.uuid');
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from static::commonAttributeMapCases($from, $expected);
        yield from static::revisionAttributeMapCases($from, $expected);
        yield from static::titleGroupMapCases($from, $expected);

        $from->setBody('body');
        $expected->setBody('body');
        yield 'body' => [deep_copy($from), deep_copy($expected)];
    }
}
