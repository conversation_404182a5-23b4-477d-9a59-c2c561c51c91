<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlDefinitionListMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlDefinitionListMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private CodeBookToXmlDefinitionListMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlDefinitionListMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\DefinitionList $from, Xml2\DefinitionList $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    public static function mapCases(): iterable
    {
        $from = new CodeBook\DefinitionList();
        $from->setNodeId('from.definitionList');
        $from->setUlid('fron.definitionList.uuid');

        $expected = new Xml2\DefinitionList();
        $expected->setId('from.definitionList');
        $expected->setCtUuid('fron.definitionList.uuid');
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from static::commonAttributeMapCases($from, $expected);
        yield from static::revisionAttributeMapCases($from, $expected);
        yield from static::titleGroupMapCases($from, $expected);

        $fromDefinition = new CodeBook\Definition();
        $fromDefinition->setNodeId('child.definition');
        $fromDefinition->setUlid('child.definition.uuid');
        $from->addChild($fromDefinition);

        $expectedDefinition = new Xml2\DefinitionItem();
        $expectedDefinition->setId('child.definition');
        $expectedDefinition->setCtUuid('child.definition.uuid');
        $expected->addChild($expectedDefinition);
        yield 'definition' => [deep_copy($from), deep_copy($expected)];

        $fromRelocatedFrom = new CodeBook\RelocatedFrom();
        $fromRelocatedFrom->setNodeId('child.relocatedFrom');
        $fromRelocatedFrom->setUlid('child.relocatedFrom.uuid');
        $from->addChild($fromRelocatedFrom);

        $expectedRelocatedFrom = new Xml2\RelocatedFrom();
        $expectedRelocatedFrom->setId('child.relocatedFrom');
        $expectedRelocatedFrom->setCtUuid('child.relocatedFrom.uuid');
        $expected->addChild($expectedRelocatedFrom);
        yield 'relocatedFrom' => [deep_copy($from), deep_copy($expected)];

        $fromRelocatedTo = new CodeBook\RelocatedTo();
        $fromRelocatedTo->setNodeId('child.relocatedTo');
        $fromRelocatedTo->setUlid('child.relocatedTo.uuid');
        $from->addChild($fromRelocatedTo);

        $expectedRelocatedTo = new Xml2\RelocatedTo();
        $expectedRelocatedTo->setId('child.relocatedTo');
        $expectedRelocatedTo->setCtUuid('child.relocatedTo.uuid');
        $expected->addChild($expectedRelocatedTo);
        yield 'relocatedTo' => [deep_copy($from), deep_copy($expected)];
    }
}
