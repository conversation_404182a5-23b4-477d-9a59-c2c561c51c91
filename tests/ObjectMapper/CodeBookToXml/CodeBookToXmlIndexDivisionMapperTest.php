<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlIndexDivisionMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlIndexDivisionMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private ?CodeBookToXmlIndexDivisionMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlIndexDivisionMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\IndexDivision $from, Xml2\IndexDivision $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    public static function mapCases(): iterable
    {
        $from = new CodeBook\IndexDivision();
        $from->setNodeId('from.index');
        $from->setUlid('from.index.uuid');

        $expected = new Xml2\IndexDivision();
        $expected->setId('from.index');
        $expected->setCtUuid('from.index.uuid');
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from static::commonAttributeMapCases($from, $expected);
        yield from static::revisionAttributeMapCases($from, $expected);
        yield from static::titleGroupMapCases($from, $expected);

        $fromEntry = new CodeBook\IndexEntry();
        $fromEntry->setNodeId('code.entry');
        $fromEntry->setUlid('code.entry.uuid');
        $from->addChild($fromEntry);

        $expectedPrimary = new Xml2\PrimaryIndexEntry();
        $expectedPrimary->setId('code.entry');
        $expectedPrimary->setCtUuid('code.entry.uuid');

        $expectedEntry = new Xml2\IndexEntry();
        $expectedEntry->setPrimaryIndexEntry($expectedPrimary);
        $expected->addChild($expectedEntry);
        yield 'index-entry' => [deep_copy($from), deep_copy($expected)];

        $fromEntry = new CodeBook\IndexEntry();
        $fromEntry->setNodeId('code.entry2');
        $fromEntry->setUlid('code.entry2.uuid');
        $from->addChild($fromEntry);

        $expectedPrimary = new Xml2\PrimaryIndexEntry();
        $expectedPrimary->setId('code.entry2');
        $expectedPrimary->setCtUuid('code.entry2.uuid');

        $expectedEntry = new Xml2\IndexEntry();
        $expectedEntry->setPrimaryIndexEntry($expectedPrimary);
        $expected->addChild($expectedEntry);
        yield 'index-entry2' => [deep_copy($from), deep_copy($expected)];
    }
}
