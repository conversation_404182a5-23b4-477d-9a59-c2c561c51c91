<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\CodeBookToXml;

use App\Entity\CodeBook;
use App\Enum\FloatEnum;
use App\Enum\Orientation;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlFigureMapper;
use App\ObjectMapper\CodeBookToXmlMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;

use function DeepCopy\deep_copy;

class CodeBookToXmlFigureMapperTest extends AbstractCodeBookToXmlMapperTestCase
{
    private CodeBookToXmlFigureMapper $mapper;
    private CodeBookToXmlMapper $codeBookToXmlMapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(CodeBookToXmlFigureMapper::class);
        $this->codeBookToXmlMapper = self::getContainer()->get(CodeBookToXmlMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CodeBook\Figure $from, Xml2\Figure $expected): void
    {
        $actual = $this->mapper->map($from);
        $this->assertEquals($expected, $actual);
    }

    public static function mapCases(): iterable
    {
        $from = new CodeBook\Figure();
        $from->setNodeId('from.figure');
        $from->setUlid('fron.figure.uuid');
        $expected = new Xml2\Figure();
        $expected->setId('from.figure');
        $expected->setCtUuid('fron.figure.uuid');
        yield 'baseline' => [deep_copy($from), deep_copy($expected)];

        yield from static::commonAttributeMapCases($from, $expected);
        yield from static::revisionAttributeMapCases($from, $expected);
        yield from static::titleGroupMapCases($from, $expected);

        $from->setTocEntry(true);
        $expected->setTocEntry(true);
        yield 'attrs / @tocEntry' => [deep_copy($from), deep_copy($expected)];

        $from->setFloat(FloatEnum::TOP);
        $expected->setFloat(FloatEnum::TOP);
        yield 'attrs / @float' => [deep_copy($from), deep_copy($expected)];

        $from->setOrientation(Orientation::LANDSCAPE);
        $expected->setOrientation(Orientation::LANDSCAPE);
        yield 'attrs / @orient' => [deep_copy($from), deep_copy($expected)];

        $from->setMedia('<mediaobject/>');
        $expected->setMedia('<mediaobject/>');
        yield 'media 1' => [deep_copy($from), deep_copy($expected)];

        $from->setMedia('<mediaobject/><mediaobject-group/>');
        $expected->setMedia('<mediaobject/><mediaobject-group/>');
        yield 'media 2' => [deep_copy($from), deep_copy($expected)];

        $from->setCaption('caption');
        $caption = new Xml2\Caption();
        $caption->setBody('caption');
        $expected->setCaption($caption);
        yield 'caption' => [deep_copy($from), deep_copy($expected)];

        $from->setFigureNotes('figureNotes');
        $figureNotes = new Xml2\FigureNotes();
        $figureNotes->setBody('figureNotes');
        $expected->setFigureNotes($figureNotes);
        yield 'figureNotes' => [deep_copy($from), deep_copy($expected)];

        $from->setFigureNotesTitle('figureNotesTitle');
        $title = new Xml2\Title();
        $title->setBody('figureNotesTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $figureNotes->setTitleGroup($titleGroup);
        yield 'figureNotesTitle' => [deep_copy($from), deep_copy($expected)];

        $from->setLegend('<legend/>');
        $expected->setLegend('<legend/>');
        yield 'legend 1' => [deep_copy($from), deep_copy($expected)];

        $from->setLegend('<legend/><legend/>');
        $expected->setLegend('<legend/><legend/>');
        yield 'legend 2' => [deep_copy($from), deep_copy($expected)];

        $from->setSource('source');
        $source = new Xml2\Source();
        $source->setBody('source');
        $expected->setSource($source);
        yield 'source' => [deep_copy($from), deep_copy($expected)];

        $from->setCredit('credit');
        $credit = new Xml2\Credit();
        $credit->setBody('credit');
        $expected->setCredit($credit);
        yield 'credit' => [deep_copy($from), deep_copy($expected)];

        $from->setCreditTitle('creditTitle');
        $title = new Xml2\Title();
        $title->setBody('creditTitle');
        $titleGroup = new Xml2\TitleGroup();
        $titleGroup->setTitle($title);
        $credit->setTitleGroup($titleGroup);
        yield 'creditTitle' => [deep_copy($from), deep_copy($expected)];
    }

    public function testFigureInSection(): void
    {
        $from = new CodeBook\Section();
        $from->setNodeId('from.section');
        $from->setUlid('fron.section.uuid');
        $expected = new Xml2\Section();
        $expected->setId('from.section');
        $expected->setCtUuid('fron.section.uuid');

        $figure1 = new CodeBook\Figure();
        $figure1->setNodeId('from.figure1');
        $figure1->setUlid('from.figure1.uuid');
        $from->addChild($figure1);

        $expectedFigure1 = new Xml2\Figure();
        $expectedFigure1->setId('from.figure1');
        $expectedFigure1->setCtUuid('from.figure1.uuid');
        $expected->addChild($expectedFigure1);

        $actual = $this->codeBookToXmlMapper->map($from);
        $this->assertEquals($expected, $actual);
    }
}
