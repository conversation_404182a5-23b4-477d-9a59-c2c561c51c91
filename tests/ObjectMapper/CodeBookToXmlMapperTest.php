<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Appendix;
use App\Entity\CodeBook\BackMatter;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\CopyrightPage;
use App\Entity\CodeBook\Definition;
use App\Entity\CodeBook\DefinitionList;
use App\Entity\CodeBook\Equation;
use App\Entity\CodeBook\Figure;
use App\Entity\CodeBook\Foreword;
use App\Entity\CodeBook\FrontMatter;
use App\Entity\CodeBook\Index;
use App\Entity\CodeBook\IndexDivision;
use App\Entity\CodeBook\IndexEntry;
use App\Entity\CodeBook\Preface;
use App\Entity\CodeBook\Promulgator;
use App\Entity\CodeBook\Publication;
use App\Entity\CodeBook\PublisherNote;
use App\Entity\CodeBook\ReferenceStandard;
use App\Entity\CodeBook\RelocatedFrom;
use App\Entity\CodeBook\RelocatedTo;
use App\Entity\CodeBook\SecondaryIndexEntry;
use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Table;
use App\Entity\CodeBook\TertiaryIndexEntry;
use App\Entity\CodeBook\TitlePage;
use App\Entity\CodeBook\Volume;
use App\Exception\ApiException;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlAppendixMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlBackMatterMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlChapterMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlCopyrightPageMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlDefinitionListMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlDefinitionMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlEquationMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlFigureMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlForewordMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlFrontMatterMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlIndexDivisionMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlIndexEntryMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlIndexMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlPrefaceMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlPromulgatorMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlPublicationMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlPublisherNoteMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlReferenceStandardMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlRelocatedFromMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlRelocatedToMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlSecondaryIndexEntryMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlSectionMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlTableMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlTertiaryIndexEntryMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlTitlePageMapper;
use App\ObjectMapper\CodeBookToXml\CodeBookToXmlVolumeMapper;
use App\ObjectMapper\CodeBookToXmlMapper;
use App\Serializer\Encoder\Xml2\Element as Xml2;
use Doctrine\Persistence\Proxy;
use PHPUnit\Framework\TestCase;

/** @covers \App\ObjectMapper\CodeBookToXmlMapper */
class CodeBookToXmlMapperTest extends TestCase
{
    private CodeBookToXmlMapper $mapper;
    private array $elementMappers = [];

    protected function setUp(): void
    {
        $this->elementMappers = [
            Appendix::class            => $this->createMock(CodeBookToXmlAppendixMapper::class),
            BackMatter::class          => $this->createMock(CodeBookToXmlBackMatterMapper::class),
            Chapter::class             => $this->createMock(CodeBookToXmlChapterMapper::class),
            CopyrightPage::class       => $this->createMock(CodeBookToXmlCopyrightPageMapper::class),
            Definition::class          => $this->createMock(CodeBookToXmlDefinitionMapper::class),
            DefinitionList::class      => $this->createMock(CodeBookToXmlDefinitionListMapper::class),
            Equation::class            => $this->createMock(CodeBookToXmlEquationMapper::class),
            Figure::class              => $this->createMock(CodeBookToXmlFigureMapper::class),
            Foreword::class            => $this->createMock(CodeBookToXmlForewordMapper::class),
            FrontMatter::class         => $this->createMock(CodeBookToXmlFrontMatterMapper::class),
            Index::class               => $this->createMock(CodeBookToXmlIndexMapper::class),
            IndexDivision::class       => $this->createMock(CodeBookToXmlIndexDivisionMapper::class),
            IndexEntry::class          => $this->createMock(CodeBookToXmlIndexEntryMapper::class),
            Preface::class             => $this->createMock(CodeBookToXmlPrefaceMapper::class),
            Promulgator::class         => $this->createMock(CodeBookToXmlPromulgatorMapper::class),
            Publication::class         => $this->createMock(CodeBookToXmlPublicationMapper::class),
            PublisherNote::class       => $this->createMock(CodeBookToXmlPublisherNoteMapper::class),
            ReferenceStandard::class   => $this->createMock(CodeBookToXmlReferenceStandardMapper::class),
            RelocatedFrom::class       => $this->createMock(CodeBookToXmlRelocatedFromMapper::class),
            RelocatedTo::class         => $this->createMock(CodeBookToXmlRelocatedToMapper::class),
            Section::class             => $this->createMock(CodeBookToXmlSectionMapper::class),
            SecondaryIndexEntry::class => $this->createMock(CodeBookToXmlSecondaryIndexEntryMapper::class),
            Table::class               => $this->createMock(CodeBookToXmlTableMapper::class),
            TertiaryIndexEntry::class  => $this->createMock(CodeBookToXmlTertiaryIndexEntryMapper::class),
            TitlePage::class           => $this->createMock(CodeBookToXmlTitlePageMapper::class),
            Volume::class              => $this->createMock(CodeBookToXmlVolumeMapper::class),
        ];

        $this->mapper = new CodeBookToXmlMapper(
            appendixMapper: $this->elementMappers[Appendix::class],
            backMatterMapper: $this->elementMappers[BackMatter::class],
            chapterMapper: $this->elementMappers[Chapter::class],
            copyrightPageMapper: $this->elementMappers[CopyrightPage::class],
            definitionMapper: $this->elementMappers[Definition::class],
            definitionListMapper: $this->elementMappers[DefinitionList::class],
            equationMapper: $this->elementMappers[Equation::class],
            figureMapper: $this->elementMappers[Figure::class],
            forewordMapper: $this->elementMappers[Foreword::class],
            frontMatterMapper: $this->elementMappers[FrontMatter::class],
            indexMapper: $this->elementMappers[Index::class],
            indexDivisionMapper: $this->elementMappers[IndexDivision::class],
            indexEntryMapper: $this->elementMappers[IndexEntry::class],
            prefaceMapper: $this->elementMappers[Preface::class],
            promulgatorMapper: $this->elementMappers[Promulgator::class],
            publicationMapper: $this->elementMappers[Publication::class],
            publisherNoteMapper: $this->elementMappers[PublisherNote::class],
            referenceStandardMapper: $this->elementMappers[ReferenceStandard::class],
            relocatedFromMapper: $this->elementMappers[RelocatedFrom::class],
            relocatedToMapper: $this->elementMappers[RelocatedTo::class],
            secondaryIndexEntryMapper: $this->elementMappers[SecondaryIndexEntry::class],
            sectionMapper: $this->elementMappers[Section::class],
            tableMapper: $this->elementMappers[Table::class],
            tertiaryIndexEntryMapper: $this->elementMappers[TertiaryIndexEntry::class],
            titlePageMapper: $this->elementMappers[TitlePage::class],
            volumeMapper: $this->elementMappers[Volume::class],
        );
    }

    /** @dataProvider mapCases */
    public function testMap(AbstractCodeBookNode $from, string $expects): void
    {
        foreach ($this->elementMappers as $class => $mapper) {
            if ($from instanceof $class) {
                $mapper
                    ->expects($this->once())
                    ->method('map')
                    ->willReturn(new $expects);
            } else {
                $mapper->expects($this->never())->method('map');
            }
        }

        $actual = $this->mapper->map($from);
        $this->assertInstanceOf($expects, $actual);
    }

    public static function mapCases(): iterable
    {
        return [
            'appendix'            => [new Appendix(), Xml2\Appendix::class],
            'backMatter'          => [new BackMatter(), Xml2\BackMatter::class],
            'chapter'             => [new Chapter(), Xml2\Level::class],
            'copyrightPage'       => [new CopyrightPage(), Xml2\CopyrightPage::class],
            'definition'          => [new Definition(), Xml2\DefinitionItem::class],
            'definitionList'      => [new DefinitionList(), Xml2\DefinitionList::class],
            'equation'            => [new Equation(), Xml2\Equation::class],
            'figure'              => [new Figure(), Xml2\Figure::class],
            'foreword'            => [new Foreword(), Xml2\Foreword::class],
            'frontMatter'         => [new FrontMatter(), Xml2\FrontMatter::class],
            'index'               => [new Index(), Xml2\Index::class],
            'indexDivision'       => [new IndexDivision(), Xml2\IndexDivision::class],
            'indexEntry'          => [new IndexEntry(), Xml2\IndexEntry::class],
            'preface'             => [new Preface(), Xml2\Preface::class],
            'promulgator'         => [new Promulgator(), Xml2\Promulgator::class],
            'publication'         => [new Publication(), Xml2\Publication::class],
            'publisherNote'       => [new PublisherNote(), Xml2\PublisherNote::class],
            'referenceStandard'   => [new ReferenceStandard(), Xml2\Reference::class],
            'relocatedFrom'       => [new RelocatedFrom(), Xml2\RelocatedFrom::class],
            'relocatedTo'         => [new RelocatedTo(), Xml2\RelocatedTo::class],
            'secondaryIndexEntry' => [new SecondaryIndexEntry(), Xml2\SecondaryIndexEntry::class],
            'section'             => [new Section(), Xml2\Section::class],
            'table'               => [new Table(), Xml2\Table::class],
            'tertiaryIndexEntry'  => [new TertiaryIndexEntry(), Xml2\TertiaryIndexEntry::class],
            'titlePage'           => [new TitlePage(), Xml2\TitlePage::class],
            'volume'              => [new Volume(), Xml2\Volume::class],
        ];
    }

    public function testMapPublicationProxy(): void
    {
        $proxy = new class extends Publication implements Proxy {
            public bool $initialized = false;

            public function __load(): void
            {
                $this->initialized = true;
            }

            public function __isInitialized(): bool
            {
                return $this->initialized;
            }
        };

        $publicationMapper = $this->elementMappers[Publication::class];
        $publicationMapper
            ->expects($this->once())
            ->method('map')
            ->with($proxy)
            ->willReturn(new Xml2\Publication());

        $actual = $this->mapper->map($proxy);

        $this->assertTrue($proxy->initialized);
        $this->assertInstanceOf(Xml2\Publication::class, $actual);
    }

    public function testMapException(): void
    {
        $this->expectException(ApiException::class);
        $invalid = new class extends AbstractCodeBookNode {
        };
        $this->mapper->map($invalid);
    }
}
