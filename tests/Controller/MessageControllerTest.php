<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;

use function count;
use function json_decode;
use function sprintf;

/**
 * @covers \App\Controller\MessageController
 */
class MessageControllerTest extends ApiTestCase
{
    public function testMessages(): void
    {
        $em = self::getContainer()->get(EntityManagerInterface::class);
        $sql = <<<SQL
INSERT INTO messenger_messages (body, headers, queue_name, created_at, available_at, delivered_at)
VALUES ("", "", "%s", CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null)
SQL;

        for ($i = 0; $i < 5; ++$i) {
            $em->getConnection()->executeQuery(sprintf($sql, 'default'));
        }
        $em->getConnection()->executeQuery(sprintf($sql, 'failed'));

        $client = static::createApiClient($this->ADMIN);
        $client->jsonRequest('GET', '/api/debug/messages');

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $json = json_decode($client->getResponse()->getContent(), true);
        $this->assertIsArray($json);
        $this->assertArrayHasKey('queue', $json);
        $this->assertIsArray($json['queue']);
        $this->assertGreaterThanOrEqual(5, count($json['queue']));
        $this->assertArrayHasKey('failed', $json);
        $this->assertIsArray($json['failed']);
        $this->assertGreaterThanOrEqual(1, count($json['failed']));
    }
}
