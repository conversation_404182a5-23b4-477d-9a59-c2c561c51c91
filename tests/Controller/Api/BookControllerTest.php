<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\Api;

use App\Dto\Xml2\Book\ListContentsResponse;
use App\Dto\Xml2\Book\NodeParentsResponse;
use App\Dto\Xml2\Book\NodeReferencingResponse;
use App\Entity\Project;
use App\Message\Project\ExportProjectMessage;
use App\Repository\ProjectRepository;
use App\Service\Xml2\ContentsService;
use App\Tests\Controller\ApiTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\BrowserKit\AbstractBrowser;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

use function json_encode;

/**
 * @covers \App\Controller\Api\BookController
 */
class BookControllerTest extends ApiTestCase
{
    private ContentsService $contentsService;
    private MessageBusInterface $bus;
    private Project $project;

    protected function setUp(): void
    {
        $container = static::getContainer();
        $em = $container->get(EntityManagerInterface::class);

        $this->contentsService = $this->createMock(ContentsService::class);
        $container->set(ContentsService::class, $this->contentsService);

        $this->bus = $this->createMock(MessageBusInterface::class);
        $container->set(MessageBusInterface::class, $this->bus);

        $this->project = new Project();
        $this->project->setShortCode('TEST');
        $em->persist($this->project);

        $em->flush();
    }

    public function testList(): void
    {
        $uri = '/api/v2/books';
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertIsArray($response);
        $this->assertArrayHasKey('books', $response);
        $this->assertGreaterThanOrEqual(1, count($response['books']));
    }

    public function testContents(): void
    {
        $this->contentsService
            ->expects($this->once())
            ->method('getContents')
            ->with($this->equalTo($this->project))
            ->willReturn($this->createMock(ListContentsResponse::class));

        $uri = '/api/v2/books/TEST/contents';
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testGetNodeParents(): void
    {
        $this->contentsService
            ->expects($this->once())
            ->method('getNodeParents')
            ->with('uuid')
            ->willReturn($this->createMock(NodeParentsResponse::class));

        $uri = '/api/v2/books/uuid/parents';
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testGetNodeReferencing(): void
    {
        $this->contentsService
            ->expects($this->once())
            ->method('getNodeReferencing')
            ->with($this->equalTo($this->project), 'test')
            ->willReturn($this->createMock(NodeReferencingResponse::class));

        $uri = '/api/v2/books/TEST/section-xml?rid=test';
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testGetNodeReferencingMissingRefId(): void
    {
        $this->contentsService->expects($this->never())->method('getNodeReferencing');

        $uri = '/api/v2/books/TEST/section-xml';
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testCHubExport(): void
    {
        $data = [
            'zipFile'           => 'zipFile',
            'isClean'           => true,
            'xmlTitle'          => 'xmlTitle',
            'versionId'         => 'versionId',
            'parentDocument'    => 'parentDocument',
            'publicationAbbrev' => 'publicationAbbrev',
            'printYear'         => 'printYear',
            'skip'              => 'skip',
            'ingest'            => 'ingest',
            'changeReason'      => 'changeReason',
        ];

        $this->bus
            ->expects($this->once())
            ->method('dispatch')
            ->willReturnCallback(function ($message) use ($data) {
                $this->assertInstanceOf(ExportProjectMessage::class, $message);
                $request = $message->getRequest();
                array_walk($data, fn($value, $key) => $this->assertEquals($request->$key, $value));
                return new Envelope($message);
            });

        $uri = '/api/v2/books/TEST/chub-export';
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('POST', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }
}
