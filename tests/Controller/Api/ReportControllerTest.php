<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\Api;

use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\Section;
use App\Entity\Project;
use App\Tests\Controller\ApiTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\BrowserKit\AbstractBrowser;
use Symfony\Component\HttpFoundation\Response;

use function array_map;
use function preg_split;
use function sprintf;
use function str_getcsv;
use function trim;

class ReportControllerTest extends ApiTestCase
{
    private AbstractBrowser $client;
    private EntityManagerInterface $em;
    private Project $project;
    private Chapter $chapter;

    protected function setUp(): void
    {
        $this->client = static::createApiClient($this->SUPER_ADMIN);
        $container = self::getContainer();

        $this->em = $container->get(EntityManagerInterface::class);
        $this->project = new Project();
        $this->project->setShortCode('report-test');
        $this->em->persist($this->project);

        $this->chapter = new Chapter();
        $this->chapter->setNodeId('chapter');
        $this->em->persist($this->chapter);

        $section = new Section();
        $section->setNodeId('section');
        $this->chapter->addChild($section);
        $this->em->persist($section);

        $this->em->flush();
    }

    public function testXmlValidationCsvReturnsNoContentWhenNoIssues(): void
    {
        $uri = sprintf('/api/v2/reports/report-test/chapters/%s/xml-validation.csv', $this->chapter->getNodeId());
        $this->client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        $this->assertSame('', $this->client->getResponse()->getContent());
    }

    public function testXmlValidationCsvExportContainsExpectedColumns(): void
    {
        $section = $this->locateFirstSection();
        $originalBody = $section->getBody();
        $section->setBody('<para>Missing closing tag');
        $this->em->flush();

        try {
            $uri = sprintf('/api/v2/reports/report-test/chapters/%s/xml-validation.csv', $this->chapter->getNodeId());
            $this->client->xmlHttpRequest('GET', $uri);

            $this->assertResponseIsSuccessful();
            $this->assertResponseStatusCodeSame(Response::HTTP_OK);
            $this->assertStringContainsString(
                'text/csv',
                (string) $this->client->getResponse()->headers->get('content-type')
            );

            $rawCsv = trim($this->client->getResponse()->getContent());
            $lines = $rawCsv === '' ? [] : preg_split("/\r\n|\n|\r/", $rawCsv);
            $this->assertNotEmpty($lines);

            $rows = array_map(static fn(string $line) => str_getcsv($line, escape: "\\"), $lines);
            $this->assertGreaterThanOrEqual(2, $rows);
            $this->assertEquals(['number', 'sectionId', 'type', 'title', 'body'], $rows[0]);
            $this->assertSame($section->getNumber() ?? '', $rows[1][0]);  // number
            $this->assertSame($section->getNodeId(), $rows[1][1]);        // sectionId
            $this->assertSame($section->getDataType(), $rows[1][2]);      // type
            $this->assertSame($section->getTitle() ?? '', $rows[1][3]);   // title
            $this->assertNotSame('', $rows[1][4]);                       // body
        } finally {
            $section->setBody($originalBody);
            $this->em->flush();
        }
    }

    private function locateFirstSection(): Section
    {
        foreach ($this->chapter->getIterator() as $node) {
            if ($node instanceof Section) {
                return $node;
            }
        }

        self::fail('Expected chapter to contain at least one section.');
    }
}
