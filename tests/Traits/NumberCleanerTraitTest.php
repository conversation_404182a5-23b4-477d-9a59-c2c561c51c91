<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Traits;

use App\Traits\NumberCleanerTrait;
use PHPUnit\Framework\TestCase;

class NumberCleanerTraitTest extends TestCase
{
    use NumberCleanerTrait;

    /** @dataProvider normalizeNumberProvider */
    public function testNormalizeNumber(string $input, bool $keepPrefix, string $expected): void
    {
        $result = $this->normalizeNumber($input, $keepPrefix);
        $this->assertEquals($expected, $result);
    }

    public static function normalizeNumberProvider(): array
    {
        return [
            ['ABC 123', false, '123'],
            ['XYZ 456', false, '456'],
            ['abc 789', false, '789'],
            ['ABC 123', true, 'ABC 123'],
            ['123 (some note)', false, '123'],
            ['456 (incomplete)', false, '456'],
            ['789 (note) more text', false, '789 more text'],
            ['ABC 123 (note)', false, '123'],
            ['ABC 123 (note)', true, 'ABC 123'],
            ['  123  ', false, '123'],
            ['123   456', false, '123 456'],
            ["123\xC2\xA0456", false, '123 456'],
            ['', false, ''],
            ['   ', false, ''],
        ];
    }

    /** @dataProvider getCleanOrdinalProvider */
    public function testGetCleanOrdinal(string $input, bool $preferInsert, string $expected): void
    {
        $result = $this->getCleanOrdinal($input, $preferInsert);
        $this->assertEquals($expected, $result);
    }

    public static function getCleanOrdinalProvider(): array
    {
        return [
            ['<insert>123</insert>', true, '123'],
            ['<ins>456</ins>', true, '456'],
            ['<insert class="test">789</insert>', true, '789'],
            ['<ins class="test">abc</ins>', true, 'abc'],

            ['<delete>123</delete>', false, '123'],
            ['<del>456</del>', false, '456'],
            ['<delete class="test">789</delete>', false, '789'],
            ['<del class="test">abc</del>', false, 'abc'],

            ['<insert>insert_val</insert><delete>delete_val</delete>', true, 'insert_val'],
            ['<insert>insert_val</insert><delete>delete_val</delete>', false, 'delete_val'],

            ['<insert>incomplete', true, 'incomplete'],
            ['<ins>incomplete', true, 'incomplete'],
            ["<ins><insert data-changed=\"added_current\" revision=\"added\">313.2", true, '313.2'],
            ["<insert data-changed=\"added_current\" revision=\"added\"><ins><insert data-changed=\"added_current\" revision=\"added\">313.2</insert></ins></insert>", true, '313.2'],

            ['<insert>keep</insert> and <delete>remove</delete>', true, 'keep'],
            ['plain text with <insert>tags</insert> removed', true, 'tags'],

            ['123 (note)', true, '123'],
            ['456 (incomplete', true, '456'],
            ['"quoted text"', true, 'quoted text'],
            ["'single quoted'", true, 'single quoted'],

            ["123\u{200B}456", true, '123456'],

            ["123\xC2\xA0456", true, '123 456'],
            ["123\xE2\x80\xAF456", true, '123 456'],

            ['', true, ''],
            ['   ', true, ''],
            ['<insert></insert>', true, ''],
        ];
    }

    public function testStripInvisibleMarks(): void
    {
        $testCases = [
            '123 (note)' => '123',
            '456 (note1) (note2)' => '456',
            '789 (incomplete' => '789',
            '"quoted"' => 'quoted',
            "'single'" => 'single',
            '  spaced  ' => 'spaced',
            "tab\tspaced" => 'tab spaced',
            "format\u{200B}chars" => 'formatchars',
        ];

        foreach ($testCases as $input => $expected) {
            $result = $this->getCleanOrdinal($input);
            $this->assertEquals($expected, $result, "Failed for input: '$input'");
        }
    }

    public function testNormalizeSpaces(): void
    {
        $testCases = [
            "text\xC2\xA0with\xC2\xA0spaces" => 'text with spaces',
            'multiple   spaces' => 'multiple spaces',
            "tab\tand\nspaces" => 'tab and spaces',
            '  trimmed  ' => 'trimmed',
        ];

        foreach ($testCases as $input => $expected) {
            $result = $this->normalizeNumber($input, true);
            $this->assertEquals($expected, $result, "Failed for input: '$input'");
        }
    }
}
