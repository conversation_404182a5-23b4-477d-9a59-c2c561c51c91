<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Traits;

use App\Traits\DefinitionTermOrderingTrait;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use App\Entity\CodeBook\Definition;

class DefinitionTermOrderingTraitTest extends KernelTestCase
{
    /** @dataProvider getDefinitionList */
    public function testSortDefinitionTermSortsAlphabeticallySuccess(Definition $definition, string $expected): void
    {
        $definitionList = [$definition];

        // Create a class that uses the trait
        $traitUser = new class {
            use DefinitionTermOrderingTrait;
        };

        // Call the method on the anonymous class
        $traitUser->sortDefinitionTerm($definitionList);
        // Actual sorted XML
        $sortedXml = $definition->getDefinition();
        // Assert they are identical
        $this->assertSame($expected, $sortedXml, 'Definition Terms was not sorted as expected');
    }

    public static function getDefinitionList(): iterable
    {
        // $entity = new Definition();
        $definitionListOne = new Definition();
        $definitionListOne->setDefinition('<p>A <phrase>structure</phrase> that uses air-pressurized membrane beams, arches or other elements to enclose space. Occupants of such a <phrase>structure</phrase> do not occupy the pressurized area used to support the <phrase>structure</phrase>.</p><def-item id="afa438ea-9ce4-4b78-acb7-d10668f20e38"><committee-desig>&lt;p&gt;Plastic Composite&lt;/p&gt;</committee-desig><term>&lt;p&gt;Plastic Composite&lt;/p&gt;</term><def><p>&lt;p&gt;Plastic Composite&lt;/p&gt;</p><def-list><def-item><committee-desig>&lt;p&gt;Roof Replacement&lt;/p&gt;</committee-desig><term>&lt;p&gt;Roof Replacement&lt;/p&gt;</term><def><p>&lt;p&gt;Roof Replacement&lt;/p&gt;</p></def></def-item><def-item><committee-desig>&lt;p&gt;Medical Care&lt;/p&gt;</committee-desig><term>&lt;p&gt;Medical Care&lt;/p&gt;</term><def><p>&lt;p&gt;Medical Care&lt;/p&gt;</p></def></def-item></def-list></def></def-item>');
        $expectedSortedXmlOne = '<p>A <phrase>structure</phrase> that uses air-pressurized membrane beams, arches or other elements to enclose space. Occupants of such a <phrase>structure</phrase> do not occupy the pressurized area used to support the <phrase>structure</phrase>.</p><def-item id="afa438ea-9ce4-4b78-acb7-d10668f20e38"><committee-desig>&lt;p&gt;Plastic Composite&lt;/p&gt;</committee-desig><term>&lt;p&gt;Plastic Composite&lt;/p&gt;</term><def><p>&lt;p&gt;Plastic Composite&lt;/p&gt;</p><def-list><def-item><committee-desig>&lt;p&gt;Medical Care&lt;/p&gt;</committee-desig><term>&lt;p&gt;Medical Care&lt;/p&gt;</term><def><p>&lt;p&gt;Medical Care&lt;/p&gt;</p></def></def-item><def-item><committee-desig>&lt;p&gt;Roof Replacement&lt;/p&gt;</committee-desig><term>&lt;p&gt;Roof Replacement&lt;/p&gt;</term><def><p>&lt;p&gt;Roof Replacement&lt;/p&gt;</p></def></def-item></def-list></def></def-item>';

        $definitionListTwo = new Definition();
        $definitionListTwo->setDefinition('<p>A <phrase>structure</phrase> that uses air-pressurized membrane beams, arches or other elements to enclose space. Occupants of such a <phrase>structure</phrase> do not occupy the pressurized area used to support the <phrase>structure</phrase>.</p><def-item id="123fa4ed-f774-4519-a4cc-cccb9eb06cd3"><committee-desig>&lt;p&gt;Service Life&lt;/p&gt;</committee-desig><term>&lt;p&gt;Service Life&lt;/p&gt;</term><def><p>&lt;p&gt;Service Life&lt;/p&gt;</p><def-list><def-item><committee-desig>&lt;p&gt;Site Class&lt;/p&gt;</committee-desig><term>&lt;p&gt;Site Class&lt;/p&gt;</term><def><p>&lt;p&gt;Site Class&lt;/p&gt;</p><def-list><def-item><committee-desig>&lt;p&gt;Sleeping Unit&lt;/p&gt;</committee-desig><term>&lt;p&gt;Sleeping Unit&lt;/p&gt;</term><def><p>&lt;p&gt;Sleeping Unit&lt;/p&gt;</p></def></def-item><def-item><committee-desig>&lt;p&gt;Platform&lt;/p&gt;</committee-desig><term>&lt;p&gt;Platform&lt;/p&gt;</term><def><p>&lt;p&gt;Platform&lt;/p&gt;</p></def></def-item></def-list></def></def-item><def-item><committee-desig>&lt;p&gt;Operator&lt;/p&gt;</committee-desig><term>&lt;p&gt;Operator&lt;/p&gt;</term><def><p>&lt;p&gt;Operator&lt;/p&gt;</p></def></def-item></def-list></def></def-item><def-item id="afa438ea-9ce4-4b78-acb7-d10668f20e38"><committee-desig>&lt;p&gt;Plastic Composite&lt;/p&gt;</committee-desig><term>&lt;p&gt;Plastic Composite&lt;/p&gt;</term><def><p>&lt;p&gt;Plastic Composite&lt;/p&gt;</p><def-list><def-item><committee-desig>&lt;p&gt;Roof Replacement&lt;/p&gt;</committee-desig><term>&lt;p&gt;Roof Replacement&lt;/p&gt;</term><def><p>&lt;p&gt;Roof Replacement&lt;/p&gt;</p></def></def-item><def-item><committee-desig>&lt;p&gt;Medical Care&lt;/p&gt;</committee-desig><term>&lt;p&gt;Medical Care&lt;/p&gt;</term><def><p>&lt;p&gt;Medical Care&lt;/p&gt;</p></def></def-item></def-list></def></def-item>');
        $expectedSortedXmlTwo = '<p>A <phrase>structure</phrase> that uses air-pressurized membrane beams, arches or other elements to enclose space. Occupants of such a <phrase>structure</phrase> do not occupy the pressurized area used to support the <phrase>structure</phrase>.</p><def-item id="afa438ea-9ce4-4b78-acb7-d10668f20e38"><committee-desig>&lt;p&gt;Plastic Composite&lt;/p&gt;</committee-desig><term>&lt;p&gt;Plastic Composite&lt;/p&gt;</term><def><p>&lt;p&gt;Plastic Composite&lt;/p&gt;</p><def-list><def-item><committee-desig>&lt;p&gt;Medical Care&lt;/p&gt;</committee-desig><term>&lt;p&gt;Medical Care&lt;/p&gt;</term><def><p>&lt;p&gt;Medical Care&lt;/p&gt;</p></def></def-item><def-item><committee-desig>&lt;p&gt;Roof Replacement&lt;/p&gt;</committee-desig><term>&lt;p&gt;Roof Replacement&lt;/p&gt;</term><def><p>&lt;p&gt;Roof Replacement&lt;/p&gt;</p></def></def-item></def-list></def></def-item><def-item id="123fa4ed-f774-4519-a4cc-cccb9eb06cd3"><committee-desig>&lt;p&gt;Service Life&lt;/p&gt;</committee-desig><term>&lt;p&gt;Service Life&lt;/p&gt;</term><def><p>&lt;p&gt;Service Life&lt;/p&gt;</p><def-list><def-item><committee-desig>&lt;p&gt;Operator&lt;/p&gt;</committee-desig><term>&lt;p&gt;Operator&lt;/p&gt;</term><def><p>&lt;p&gt;Operator&lt;/p&gt;</p></def></def-item><def-item><committee-desig>&lt;p&gt;Site Class&lt;/p&gt;</committee-desig><term>&lt;p&gt;Site Class&lt;/p&gt;</term><def><p>&lt;p&gt;Site Class&lt;/p&gt;</p><def-list><def-item><committee-desig>&lt;p&gt;Platform&lt;/p&gt;</committee-desig><term>&lt;p&gt;Platform&lt;/p&gt;</term><def><p>&lt;p&gt;Platform&lt;/p&gt;</p></def></def-item><def-item><committee-desig>&lt;p&gt;Sleeping Unit&lt;/p&gt;</committee-desig><term>&lt;p&gt;Sleeping Unit&lt;/p&gt;</term><def><p>&lt;p&gt;Sleeping Unit&lt;/p&gt;</p></def></def-item></def-list></def></def-item></def-list></def></def-item>';


        yield 'Success Test Case 1 Terms Present' => [$definitionListOne, $expectedSortedXmlOne];
        yield 'Success Test Case 2 Nested Terms and SubTerm Present' => [$definitionListTwo, $expectedSortedXmlTwo];

        $noDefinition = new Definition();
        $noDefinition->setDefinition('<p>Definition</p>');
        $expected = '<p>Definition</p>';

        yield 'Terms Not Present' => [$noDefinition, $expected];
    }
}
